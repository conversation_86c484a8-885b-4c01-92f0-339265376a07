Phase 2.3 Development Guide: Architectural Refinement & Hardening
Goal: To refactor the existing authentication module by implementing the key suggestions from the code review. This will make the codebase more robust, maintainable, and easier to scale for future features.

Task 1: Refactor Error Handling Protocol
Rationale: The review noted that our ErrorMiddleware uses string comparisons to identify errors. This is brittle. We will refactor this to use type-safe error variables, as mandated by Rule 3.2 and 3.3.

Create Custom Middleware Errors:

File: internal/platform/server/middleware/error.go (or a new errors.go in the same package).

Action: Define and export custom error variables for errors that originate from the middleware layer itself.

// In middleware/error.go
var (

    ErrAuthHeaderRequired    = errors.New("authorization header required")
    ErrInvalidAuthFormat     = errors.New("invalid authorization header format")
    ErrInsufficientPermissions = errors.New("insufficient permissions")
    // ... and so on for other middleware-specific errors

)

Update Middleware to Use Custom Errors:

File: internal/platform/server/middleware/auth.go

Action: Modify the AuthMiddleware to return the new custom error variables instead of generic ones.

// Before
// AbortWithError(c, errors. New("authorization header required"))

// After (following Rule 3.2)
AbortWithError(c, <PERSON>rr<PERSON><PERSON><PERSON><PERSON>erRequired)

Refactor the ErrorMiddleware:

File: internal/platform/server/middleware/error.go

Action: Change the switch statement to use errors. Is() for type-safe comparisons. This is far more robust than string matching.

// Before
// case err. Error() == "authorization header required":

// After (following Rule 3.3)
switch {
case errors. Is(err, ErrAuthHeaderRequired):

    webapi.Error(c, http.StatusUnauthorized, "AUTHORIZATION_REQUIRED")

case errors. Is(err, account. ErrInvalidCredentials):

    webapi.Error(c, http.StatusUnauthorized, "INVALID_CREDENTIALS")

// ... other cases
}

Task 2: Centralize Persistence Type Converters
Rationale: The review noted that pgtype-to-domain conversion helpers are defined within the repository, which will lead to code duplication. Rule 8.3 requires these to be centralized.

Create the Converters Package:

Action: Create a new package: internal/persistence/converters.

Move and Publicize Helper Functions:

Action: Move all the pgtype-to-domain and domain-to-pgtype helper functions (e.g., uuidToPgtype, dbAccountToDomain) from account_repository.go into one or more files within the new converters package.

Action: Make the function names public (e.g., converters. DBAccountToDomain).

Update Repository to Use Converters:

File: internal/persistence/postgres/account_repository.go

Action: Remove the now-private helper functions. Update all repository methods to call the public functions from the new converters package.

Task 3: Refine Database Seeding Strategy
Rationale: The review pointed out that our migration files contain INSERT statements. Rule 9.2 mandates a strict separation of schema (DDL) and test data (DML).

Clean Up the Auth Migration:

File: migrations/000002_create_auth_tables.up.sql

Action: Delete all INSERT INTO ... statements from this file. The migration should only contain ALTER TABLE and CREATE TABLE statements.

Update the Seed File:

File: testdata/seeds/000002_seed_auth_data.sql

Action: Ensure this file contains the INSERT statements for the default roles and permissions that were removed from the migration. The test setup helper will automatically run this file after migrations, ensuring the test database is correctly populated.

Task 4: Implement Centralized Test Server Setup
Rationale: Handler tests currently repeat setup logic for the database, Redis, and services. Rule 9.5 recommends creating a DRY (Don't Repeat Yourself) test setup.

Create the Test Server Helper:

File: internal/platform/testhelpers/server.go

Action: Create a new helper function SetupTestServer(). This function will:

Call the existing SetupTestDatabase() and SetupTestRedis() helpers.

Instantiate the full dependency chain: repositories, services, and handlers.

Initialize a new Gin router and register all middleware and API routes.

Return the fully configured *gin. Engine and the cleanup functions for the database and Redis.

Refactor Handler Tests:

File: internal/platform/server/handlers/auth_handler_test.go

Action: Remove the manual router setup logic from the beginning of the test functions.

Action: Simply call router, cleanup := testhelpers. SetupTestServer() and defer cleanup() at the start of each test or test suite. This makes the tests much cleaner and easier to write and maintain.

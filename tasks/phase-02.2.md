Phase 2.2 Development Guide: API Documentation & Localization
Goal: To enhance the developer experience with modular, scriptable API documentation and to internationalize the service by adding dual-language support for English and Indonesian.

Task 1: API Documentation with Postman
This task establishes a scalable and maintainable process for API documentation.

Establish Modular Collection Structure:

Action: In the project root, create a new directory structure: docs/postman/collections.

Rationale: We will create one Postman collection JSON file per feature (e.g., auth.json, payments.json). This prevents a single massive collection file that is difficult to manage and prone to merge conflicts.

Define the Standard for Postman Requests:

Action: Establish a team convention that every request in a Postman collection MUST include:

A Detailed Description: Use Markdown in the request description field to explain the endpoint's purpose, required permissions, and parameter details.

Multiple Examples: At least one "success" example and one "failure" (e.g., validation error) example with saved response bodies.

Post-Request Scripts: Use JavaScript in the "Tests" tab to automate parts of the workflow.

Create the auth Collection Module:

File: docs/postman/collections/auth.postman_collection.json

Action: Create the first collection file for the authentication endpoints (/auth/register, /auth/login, /auth/refresh).

Post-Request Script for Login: Add the following JavaScript to the "Tests" tab of the /auth/login request. This script automatically extracts the JWT and refresh token from the response and saves them to the Postman environment, making it seamless to test protected endpoints.

// Post-request script for /auth/login
if (pm.response.code === 200) {

    try {
        const response = pm.response.json();
        if (response.data && response.data.access_token && response.data.refresh_token) {
            pm.environment.set("jwt_access_token", response.data.access_token);
            pm.environment.set("jwt_refresh_token", response.data.refresh_token);
            console.log("Access and Refresh tokens saved to environment.");
        }
    } catch (e) {
        console.log("Could not parse response JSON or find tokens.");
    }

}

Authentication for Protected Requests: For all other requests that require authentication, set the "Authorization" type to "Bearer Token" and use {{jwt_access_token}} as the token value.

Implement the Merger Script:

File: docs/postman/merge_collections.js

Action: Create a Node.js script that reads all *.json files from the /collections subdirectory, merges them into a single valid Postman collection JSON object, and saves it as payment-service.postman_collection.json in the /docs/postman directory. This provides a single, importable file for users.

CI/CD Integration: This script should be run as part of your CI/CD pipeline so the master collection is always up-to-date.

Task 2: Dual-Language Support (Localization)
This task integrates internationalization (i18n) into the core of the application.

Integrate go-i18n Library:

Action: Install the latest version of the standard go-i18n library.

go get github.com/nicksnyder/go-i18n/v2/i18n@latest

Create Translation Files:

Action: Create a new directory configs/lang. Inside, create JSON files for each supported language. These files will store key-value pairs for all user-facing strings.

File (configs/lang/en.json):

{
  "INVALID_INPUT": "The provided input is invalid.", 
  "RESOURCE_NOT_FOUND": "The requested resource could not be found."
}

File (configs/lang/id.json):

{
  "INVALID_INPUT": "Data masukan tidak valid.", 
  "RESOURCE_NOT_FOUND": "Sumber daya yang diminta tidak dapat ditemukan."
}

Implement the i18n Bundle Loader:

File: internal/platform/localization/i18n.go

Action: Create a function NewBundle() that loads all *.json language files from the configs/lang directory into a i18n. Bundle. Set English as the default fallback language.

Implement Language Detection Middleware:

File: internal/platform/server/middleware/localization.go

Action: Create a Gin middleware that:

Initializes a i18n. Localizer by passing it the i18n. Bundle and the Accept-Language header from the HTTP request.

Stores this localizer instance in the Gin context for use by handlers: c. Set("localizer", localizer).

Integrate Localization into API Responses:

Action: The final step is to make your standardized error responses language-aware.

File: internal/platform/webapi/error.go

Action: Modify the Error helper function. It should now retrieve the localizer from the Gin context. Instead of directly using the message string passed to it, it will use the code to look up the translated message from the bundle.

Code Snippet (internal/platform/webapi/error.go):

func Error(c *gin. Context, status int, code string) {

    localizer, ok := c.MustGet("localizer").(*i18n.Localizer)
    if !ok {
        // Fallback if middleware fails
        c.JSON(status, ...)
        return
    }

    localizedMsg := localizer.MustLocalize(&i18n.LocalizeConfig{
        MessageID: code,
    })

    errResp := errorResponse{
        Status: "error",
        Error:  apiError{Code: code, Message: localizedMsg},
    }
    c.JSON(status, errResp)

}

SYSTEM PROMPT: You are a senior Go developer. You will execute the following tasks sequentially to implement API documentation and localization features. You MUST strictly adhere to the CODING_GUIDELINES_V1.3 ruleset for all code generation.

TASK 1: CREATE POSTMAN DOCUMENTATION

1.1. CREATE DIRECTORY: docs/postman/collections/
1.2. CREATE FILE: docs/postman/collections/auth.postman_collection.json.
* This file MUST be a valid Postman collection (v2.1.0 schema).
* It MUST contain three requests: POST /auth/register, POST /auth/login, and POST /auth/refresh.
* Each request MUST have a Markdown description.
* Each request MUST have at least one success example.
* The /auth/login request MUST contain the following JavaScript in its "tests" event script to automatically handle environment variables:
javascript if (pm.response.code === 200) { try { const res = pm.response.json(); pm.environment.set("jwt_access_token", res.data.access_token); pm.environment.set("jwt_refresh_token", res.data.refresh_token); } catch(e) { console.error(e); } } 
1.3. CREATE FILE: docs/postman/merge_collections.js.
* This file MUST contain a Node.js script that:
a. Reads all .json files from the ./collections/ subdirectory.
b. Merges them into a single Postman collection object.
c. Writes the final, merged collection to payment-service.postman_collection.json in the current directory (./).

TASK 2: IMPLEMENT LOCALIZATION (i18n)

2.1. RUN COMMAND: go get github.com/nicksnyder/go-i18n/v2@latest
2.2. CREATE DIRECTORY: configs/lang
2.3. CREATE FILE: configs/lang/en.json with JSON content: {"INVALID_INPUT": "The provided input is invalid.", "RESOURCE_NOT_FOUND": "The requested resource could not be found."}.
2.4. CREATE FILE: configs/lang/id.json with JSON content: {"INVALID_INPUT": "Data masukan tidak valid.", "RESOURCE_NOT_FOUND": "Sumber daya yang diminta tidak dapat ditemukan."}.
2.5. CREATE FILE: internal/platform/localization/i18n.go. Implement a NewBundle() function that loads all *.json files from configs/lang into a *i18n. Bundle and sets "en" as the default language.
2.6. MODIFY FILE: cmd/server/main.go.
a. Initialize the i18n bundle by calling localization. NewBundle().
b. Pass the bundle instance where needed (e.g., to middleware).
2.7. CREATE FILE: internal/platform/server/middleware/localization.go. Implement a Gin middleware Localizer(bundle *i18n. Bundle) gin. HandlerFunc that:
a. Reads the Accept-Language header from the request.
b. Creates a *i18n. Localizer instance.
c. Stores the localizer in the Gin context via c. Set("localizer", localizer).
2.8. MODIFY FILE: cmd/server/main.go. Add the middleware. Localizer(bundle) to your Gin router's middleware chain. It should come after the Tracing middleware but before the Error middleware.
2.9. MODIFY FILE: internal/platform/webapi/error.go. Modify the Error function signature to Error(c *gin. Context, status int, code string). This function MUST now retrieve the *i18n. Localizer from the context and use it to translate the code into the final message field of the error response.
2.10. REFACTOR: Update all existing calls to webapi. Error throughout the application to remove the message argument, passing only the context, status code, and error code.

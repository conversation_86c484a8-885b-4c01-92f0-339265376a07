Coding Guidelines & Best PracticesProject: Payment Service v2Version: 1.4Date: June 26, 2025This document outlines the coding standards, patterns, and best practices for the Payment Service project. Adherence to these guidelines is mandatory for all contributors, including human developers and AI agents, to ensure a high-quality, consistent, and maintainable codebase.1. General Principles & Idiomatic Go(No changes from v1.3)Rule 1.1 (Simplicity - KISS): Prefer simple, clear solutions. Rule 1.2 (Effective Go): Follow official Go conventions. Rule 1.3 (Package Naming): All package names MUST be short-lowercase. Rule 1.4 (Variable Naming): All variable names MUST be camelCase. Rule 1.5 (Interfaces): Define interfaces to decouple components. Functions MUST accept interfaces where possible and return concrete struct types. Rule 1.6 (Formatting): All Go code MUST be formatted with gofmt.2. Project Structure & Clean Architecture(No changes from v1.3)Rule 2.1 (Strict Layering): The dependency direction API -> Application -> Domain MUST be strictly enforced. Rule 2.2 (Domain Purity): The internal/domain package MUST NOT import any other project packages. Rule 2.3 (Thin Handlers): API handlers MUST only parse requests, call a single application service method, and generate a standardized response.3. Error Management ProtocolRule 3.1 (Error Wrapping): Errors MUST be wrapped with context: fmt. Errorf("package.function: %w", err). Rule 3.2 (Custom Error Variables): Use custom error variables for predictable business rule failures (in the domain layer) and for predictable platform/middleware errors (in their respective packages). This enables type-safe error checking with errors. Is(). Rule 3.3 (Centralized HTTP Error Mapping): A dedicated Gin error-handling middleware is the only place where errors are translated into HTTP status codes. This middleware MUST use errors. Is() for checking custom error variables instead of string comparison. Rule 3.4 (Sentry for Unexpected Errors): Use webapi. ErrorWithSentry(c, err) for all 5xx internal server errors. Rule 3.5 (Error Code Mapping): Each custom error variable MUST map to a specific UPPER_SNAKE_CASE error code that exists in all language files. Rule 3.6 (Localization Context): The error handling middleware MUST retrieve the localizer from the Gin context and gracefully fall back to the default language.4. Standardized API Responses (webapi package)(No changes from v1.3)Rule 4.1 (Mandatory Helpers): All HTTP responses MUST be generated using helpers from the internal/platform/webapi package. Rule 4.2 (Success Structure): The mandatory JSON structure for success is {"status": "success", "data": { ... }}. Rule 4.3 (Error Structure): The mandatory JSON structure for an error is {"status": "error", "error": {"code": "...", "message": "..."}}. Rule 4.4 (Helper Signatures): Adhere to the defined signatures for Success, SuccessPaginated, Error, and ErrorWithSentry. Rule 4.5 (Localized Error Messages): Error messages MUST be retrieved from the i18n bundle using error codes. Rule 4.6 (Error Code Format): Error codes MUST use UPPER_SNAKE_CASE format.5. Logging Best Practices (zerolog)(No changes from v1.3)Rule 5.1 (Structured Logging): All logging MUST be done using the global zerolog instance. Rule 5.2 (Contextual Logging): Every log statement MUST include the trace_id from the context. Rule 5.3 (Error Logging): When logging an error, the error object MUST be included using . Err(err).6. Concurrency and Graceful Shutdown(No changes from v1.3)Rule 6.1 (Panic Safety): Any new goroutine MUST use a defer...recover() block, logging any recovered panic to Sentry. Rule 6.2 (Graceful Termination): Any long-running goroutine MUST accept a context. Context and listen on ctx. Done(). Rule 6.3 (Server Shutdown): main.go must implement graceful shutdown for the HTTP server.7. Middleware Guidelines(No changes from v1.3)Rule 7.1 (Tracing First): TracingMiddleware MUST be the first middleware in the chain. Rule 7.2 (Logging Second): LoggerMiddleware MUST be the second middleware in the chain. Rule 7.3 (Localization Third): LocalizationMiddleware MUST be the third middleware. Rule 7.4 (Error Last): ErrorMiddleware MUST be the last middleware in the chain. Rule 7.5 (Context Storage): Middleware MUST store required data in the Gin context using consistent keys.8. Database Access (sqlc with pgx/v5)Rule 8.1 (SQL Files): All SQL queries MUST be defined in .sql files. Rule 8.2 (sqlc.yaml Configuration): The sqlc.yaml file MUST be configured to use sql_package: "pgx/v5" and appropriate type overrides. Rule 8.3 (Centralized Type Converters): All conversions between domain model types (e.g., uuid. UUID, time. Time) and sqlc-generated pgtype types (e.g., pgtype. UUID, pgtype. Timestamptz) MUST be handled in a dedicated internal/persistence/converters package to avoid duplication. Repositories MUST use these shared converters. Rule 8.4 (Transactions): Repository methods that perform writes MUST accept pgx. Tx as a parameter.9. Testing and CI/CD StrategyRule 9.1 (Containerized Testing): All integration and end-to-end tests MUST use Testcontainers for Go (github.com/testcontainers/testcontainers-go). Rule 9.2 (Schema vs. Seed Separation): Database migration files (*.up.sql) MUST only contain schema definition language (DDL) like CREATE, ALTER, and DROP. All data manipulation language (DML) for populating test data MUST be placed in corresponding seed files in testdata/seeds/. Rule 9.3 (Test Coverage): API handlers, application services, and repositories MUST have dedicated test files. Rule 9.4 (CI/CD Gate): A pull request MUST NOT be allowed to merge into the main branch unless all tests (go test ./...) pass. Rule 9.5 (DRY Test Setup): For testing multiple handlers, a centralized test helper function (e.g., testhelpers. SetupTestServer()) SHOULD be created to return a fully configured test server instance, avoiding repeated setup logic.10. Internationalization (i18n) Guidelines(No changes from v1.3)Rule 10.1 (Language File Structure): Language files MUST be stored in configs/lang/ with ISO 639-1 language codes (e.g., en.json, id.json). Rule 10.2 (Error Code Consistency): All error codes MUST be UPPER_SNAKE_CASE and be present in ALL language files. Rule 10.3 (Middleware Placement): Localization middleware MUST be placed after Logger and before Error middleware. Rule 10.4 (Fallback Handling): All localization functions MUST gracefully fall back to English. Rule 10.5 (Accept-Language Support): The API MUST support the RFC 7231 Accept-Language header with quality value prioritization.11. API Documentation Guidelines(No changes from v1.3)Rule 11.1 (Modular Collections): Postman collections MUST be organized by feature in docs/postman/collections/. Rule 11.2 (Collection Schema): Postman collections MUST use the v2.1.0 schema. Rule 11.3 (Automated Merging): A merge script MUST be provided to combine collections. Rule 11.4 (Test Scripts): Postman collections MUST include JavaScript test scripts for validation and environment management. Rule 11.5 (Documentation Standards): Endpoints MUST include Markdown descriptions and examples. AI Agent Explicit RulesetSYSTEM PROMPT: You are a senior Go developer operating under the "MCP" (Master Coding Protocol). You will strictly adhere to the following ruleset when generating or modifying code for the payment-service-v2 project. This guide is your primary directive. RULESET: CODING_GUIDELINES_V1.4Rule 1 (General): Enforce gofmt, camelCase, short-lowercase packages, and interface-based design. Rule 2 (Architecture): Enforce API -> Application -> Domain dependency rule and thin handlers. Rule 3 (Error Handling): Enforce error wrapping and Sentry reporting. The error-handling middleware MUST use errors. Is() to check for custom error variables defined in domain or platform packages. Rule 4 (API Responses): Enforce the use of webapi helpers. Error messages MUST be generated by localizing UPPER_SNAKE_CASE error codes. Rule 5 (Logging): Enforce structured, contextual logging with zerolog. Rule 6 (Concurrency): Enforce panic safety and graceful termination of goroutines. Rule 7 (Middleware): Enforce the exact middleware order: Tracing -> Logger -> Localization -> Error. Rule 8 (Database):8.1. All SQL MUST be defined in .sql files for sqlc.8.2. All pgtype conversions MUST be performed by functions in the internal/persistence/converters package.8.3. Repository write methods MUST accept pgx. Tx as a parameter. Rule 9 (Testing & CI/CD):9.1. All integration/E2E tests MUST use testcontainers-go.9.2. Migration files (*.up.sql) MUST only contain DDL. All INSERT statements for tests MUST be in testdata/seeds/*.sql.9.3. Test coverage MUST be provided for handlers, services, and repositories.9.4. All code MUST assume it is gated by a CI pipeline that blocks merges on test failure.9.5. E2E handler tests SHOULD use a centralized testhelpers. SetupTestServer() function. Rule 10 (Internationalization): Enforce correct language file structure, middleware placement, and fallback to English. The Accept-Language header must be parsed correctly. Rule 11 (API Documentation): Enforce modular Postman collections, a merge script, and complete documentation within each request. END OF RULESET

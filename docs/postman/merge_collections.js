#!/usr/bin/env node

/**
 * Postman Collection Merger Script
 * 
 * This script reads all *.json files from the ./collections/ subdirectory,
 * merges them into a single valid Postman collection object, and writes
 * the final merged collection to payment-service.postman_collection.json
 * in the current directory.
 * 
 * Usage: node merge_collections.js
 * 
 * Requirements:
 * - Node.js 14+
 * - All collection files must be valid Postman v2.1.0 collections
 * 
 * Rules followed:
 * - Rule 1.1 (Simplicity): Simple, clear solution
 * - Proper error handling with descriptive messages
 */

const fs = require('fs');
const path = require('path');

// Configuration
const COLLECTIONS_DIR = './collections';
const OUTPUT_FILE = './payment-service.postman_collection.json';
const MERGED_COLLECTION_NAME = 'Payment Service API';
const MERGED_COLLECTION_DESCRIPTION = `
# Payment Service API Collection

This is a merged collection containing all API endpoints for the Payment Service.

## Collections Included
This collection is automatically generated by merging individual feature collections.

## Environment Variables Required
- \`base_url\`: The base URL of the Payment Service API (e.g., http://localhost:8080)
- \`jwt_access_token\`: Access token (automatically set by login request)
- \`jwt_refresh_token\`: Refresh token (automatically set by login request)

## Getting Started
1. Import this collection into Postman
2. Set up your environment with the required variables
3. Start with the Authentication endpoints to get tokens
4. Use the tokens for authenticated requests

Generated on: ${new Date().toISOString()}
`;

/**
 * Validates if a file is a valid Postman collection
 * @param {Object} collection - Parsed JSON object
 * @param {string} filename - Name of the file for error reporting
 * @returns {boolean} - True if valid
 */
function validateCollection(collection, filename) {
    if (!collection || typeof collection !== 'object') {
        console.error(`❌ Error: ${filename} is not a valid JSON object`);
        return false;
    }

    if (!collection.info) {
        console.error(`❌ Error: ${filename} missing required 'info' property`);
        return false;
    }

    if (!collection.info.schema || !collection.info.schema.includes('collection')) {
        console.error(`❌ Error: ${filename} is not a valid Postman collection (missing or invalid schema)`);
        return false;
    }

    if (!Array.isArray(collection.item)) {
        console.error(`❌ Error: ${filename} missing or invalid 'item' array`);
        return false;
    }

    return true;
}

/**
 * Reads and parses a collection file
 * @param {string} filePath - Path to the collection file
 * @returns {Object|null} - Parsed collection or null if error
 */
function readCollection(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        const collection = JSON.parse(content);
        const filename = path.basename(filePath);
        
        if (!validateCollection(collection, filename)) {
            return null;
        }

        console.log(`✅ Loaded collection: ${collection.info.name} (${collection.item.length} items)`);
        return collection;
    } catch (error) {
        console.error(`❌ Error reading ${filePath}:`, error.message);
        return null;
    }
}

/**
 * Merges multiple collections into a single collection
 * @param {Array} collections - Array of collection objects
 * @returns {Object} - Merged collection
 */
function mergeCollections(collections) {
    const mergedCollection = {
        info: {
            _postman_id: `merged-${Date.now()}`,
            name: MERGED_COLLECTION_NAME,
            description: MERGED_COLLECTION_DESCRIPTION.trim(),
            schema: "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
        },
        item: []
    };

    // Merge all items from all collections
    collections.forEach(collection => {
        if (collection && collection.item) {
            // Add collection name as a folder if there are multiple collections
            if (collections.length > 1) {
                mergedCollection.item.push({
                    name: collection.info.name,
                    description: collection.info.description || '',
                    item: collection.item
                });
            } else {
                // If only one collection, merge items directly
                mergedCollection.item.push(...collection.item);
            }
        }
    });

    return mergedCollection;
}

/**
 * Main function to merge collections
 */
function main() {
    console.log('🚀 Starting Postman collection merger...\n');

    // Check if collections directory exists
    if (!fs.existsSync(COLLECTIONS_DIR)) {
        console.error(`❌ Error: Collections directory '${COLLECTIONS_DIR}' does not exist`);
        process.exit(1);
    }

    // Read all JSON files from collections directory
    let files;
    try {
        files = fs.readdirSync(COLLECTIONS_DIR)
            .filter(file => file.endsWith('.json'))
            .map(file => path.join(COLLECTIONS_DIR, file));
    } catch (error) {
        console.error(`❌ Error reading collections directory:`, error.message);
        process.exit(1);
    }

    if (files.length === 0) {
        console.error(`❌ Error: No JSON files found in '${COLLECTIONS_DIR}'`);
        process.exit(1);
    }

    console.log(`📁 Found ${files.length} collection file(s):`);
    files.forEach(file => console.log(`   - ${path.basename(file)}`));
    console.log('');

    // Load and validate all collections
    const collections = [];
    for (const file of files) {
        const collection = readCollection(file);
        if (collection) {
            collections.push(collection);
        }
    }

    if (collections.length === 0) {
        console.error('❌ Error: No valid collections found');
        process.exit(1);
    }

    console.log(`\n✅ Successfully loaded ${collections.length} collection(s)`);

    // Merge collections
    console.log('\n🔄 Merging collections...');
    const mergedCollection = mergeCollections(collections);

    // Write merged collection to output file
    try {
        fs.writeFileSync(OUTPUT_FILE, JSON.stringify(mergedCollection, null, 2));
        console.log(`\n✅ Successfully created merged collection: ${OUTPUT_FILE}`);
        console.log(`📊 Total items in merged collection: ${mergedCollection.item.length}`);
        console.log('\n🎉 Collection merger completed successfully!');
    } catch (error) {
        console.error(`❌ Error writing output file:`, error.message);
        process.exit(1);
    }
}

// Run the script
if (require.main === module) {
    main();
}

module.exports = {
    validateCollection,
    readCollection,
    mergeCollections,
    main
};

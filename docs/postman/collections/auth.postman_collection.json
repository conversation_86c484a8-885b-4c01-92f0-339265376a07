{"info": {"_postman_id": "auth-collection-uuid", "name": "Payment Service - Authentication", "description": "Authentication endpoints for the Payment Service API. This collection includes user registration, login, and token refresh functionality.\n\n## Base URL\n`{{base_url}}/api/v1`\n\n## Environment Variables Required\n- `base_url`: The base URL of the Payment Service API (e.g., http://localhost:8080)\n- `jwt_access_token`: Access token (automatically set by login request)\n- `jwt_refresh_token`: Refresh token (automatically set by login request)\n\n## Authentication Flow\n1. Register a new user account\n2. <PERSON><PERSON> to get access and refresh tokens\n3. Use access token for authenticated requests\n4. Refresh tokens when access token expires", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Register User", "event": [{"listen": "test", "script": {"exec": ["// Test for successful registration", "pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"Response has success status\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.eql(\"success\");", "});", "", "pm.test(\"Response contains user data\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property(\"message\");", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"securepassword123\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/register", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "register"]}, "description": "# Register New User\n\nCreates a new user account in the payment service.\n\n## Request Body\n- `name` (string, required): User's full name (2-100 characters)\n- `email` (string, required): Valid email address\n- `password` (string, required): Password (minimum 8 characters)\n\n## Response\n- **201 Created**: User successfully registered\n- **400 Bad Request**: Invalid input data or validation errors\n- **409 Conflict**: Email already exists\n\n## Required Permissions\nNone - This is a public endpoint."}, "response": [{"name": "Success - User Registered", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"securepassword123\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/register", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "register"]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"data\": {\n        \"message\": \"User registered successfully\"\n    }\n}"}, {"name": "Error - <PERSON><PERSON> Already Exists", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"securepassword123\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/register", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "register"]}}, "status": "Conflict", "code": 409, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"status\": \"error\",\n    \"error\": {\n        \"code\": \"CONFLICT\",\n        \"message\": \"Account already exists\"\n    }\n}"}]}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["// Post-request script for /auth/login", "if (pm.response.code === 200) {", "    try {", "        const response = pm.response.json();", "        if (response.data && response.data.access_token && response.data.refresh_token) {", "            pm.environment.set(\"jwt_access_token\", response.data.access_token);", "            pm.environment.set(\"jwt_refresh_token\", response.data.refresh_token);", "            console.log(\"Access and Refresh tokens saved to environment.\");", "        }", "    } catch (e) {", "        console.log(\"Could not parse response JSON or find tokens.\");", "    }", "}", "", "// Test for successful login", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has success status\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.eql(\"success\");", "});", "", "pm.test(\"Response contains tokens\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property(\"access_token\");", "    pm.expect(jsonData.data).to.have.property(\"refresh_token\");", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"securepassword123\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "login"]}, "description": "# User Login\n\nAuthenticates a user and returns access and refresh tokens.\n\n## Request Body\n- `email` (string, required): User's email address\n- `password` (string, required): User's password\n\n## Response\n- **200 OK**: Login successful, returns tokens\n- **401 Unauthorized**: Invalid credentials\n- **400 Bad Request**: Invalid request format\n\n## Response Data\n- `access_token`: JWT token for API authentication\n- `refresh_token`: Token for refreshing access token\n- `expires_in`: Access token expiration time in seconds\n- `token_type`: Always \"Bearer\"\n\n## Required Permissions\nNone - This is a public endpoint.\n\n## Notes\nThe post-request script automatically saves tokens to environment variables for use in subsequent requests."}, "response": [{"name": "Success - Login", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"securepassword123\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "login"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"data\": {\n        \"access_token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\",\n        \"refresh_token\": \"abc123def456...\",\n        \"expires_in\": 3600,\n        \"token_type\": \"Bearer\"\n    }\n}"}, {"name": "Error - Invalid Credentials", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"wrongpassword\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "login"]}}, "status": "Unauthorized", "code": 401, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"status\": \"error\",\n    \"error\": {\n        \"code\": \"UNAUTHORIZED\",\n        \"message\": \"Invalid credentials\"\n    }\n}"}]}, {"name": "Refresh <PERSON>", "event": [{"listen": "test", "script": {"exec": ["// Post-request script for /auth/refresh", "if (pm.response.code === 200) {", "    try {", "        const response = pm.response.json();", "        if (response.data && response.data.access_token && response.data.refresh_token) {", "            pm.environment.set(\"jwt_access_token\", response.data.access_token);", "            pm.environment.set(\"jwt_refresh_token\", response.data.refresh_token);", "            console.log(\"New access and refresh tokens saved to environment.\");", "        }", "    } catch (e) {", "        console.log(\"Could not parse response JSON or find tokens.\");", "    }", "}", "", "// Test for successful token refresh", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has success status\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.eql(\"success\");", "});", "", "pm.test(\"Response contains new tokens\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property(\"access_token\");", "    pm.expect(jsonData.data).to.have.property(\"refresh_token\");", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"refresh_token\": \"{{jwt_refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/refresh", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "refresh"]}, "description": "# Refresh Access Token\n\nGenerates a new access token using a valid refresh token. This implements token rotation - the old refresh token is invalidated and a new one is provided.\n\n## Request Body\n- `refresh_token` (string, required): Valid refresh token from login or previous refresh\n\n## Response\n- **200 OK**: Token refresh successful, returns new tokens\n- **401 Unauthorized**: Invalid or expired refresh token\n- **400 Bad Request**: Invalid request format\n\n## Response Data\n- `access_token`: New JWT token for API authentication\n- `refresh_token`: New refresh token (old one is invalidated)\n- `expires_in`: Access token expiration time in seconds\n- `token_type`: Always \"Bearer\"\n\n## Required Permissions\nNone - This is a public endpoint, but requires a valid refresh token.\n\n## Notes\n- Token rotation is implemented for security\n- The post-request script automatically updates environment variables\n- Use this endpoint when the access token expires (typically after 1 hour)"}, "response": [{"name": "Success - <PERSON><PERSON> Refreshed", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"refresh_token\": \"abc123def456...\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/refresh", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "refresh"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"status\": \"success\",\n    \"data\": {\n        \"access_token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\",\n        \"refresh_token\": \"xyz789uvw012...\",\n        \"expires_in\": 3600,\n        \"token_type\": \"Bearer\"\n    }\n}"}, {"name": "Error - Invalid Refresh <PERSON>", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"refresh_token\": \"invalid_token\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/refresh", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "refresh"]}}, "status": "Unauthorized", "code": 401, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"status\": \"error\",\n    \"error\": {\n        \"code\": \"UNAUTHORIZED\",\n        \"message\": \"Invalid credentials\"\n    }\n}"}]}]}
package errors

import (
	"errors"
)

// Authentication and Authorization Errors
// Rule 3.2: Custom error variables for business errors
// Rule 1.2: camelCase variable names
var (
	// Authentication errors
	ErrAuthorizationRequired      = errors.New("authorization header required")
	ErrInvalidAuthorizationFormat = errors.New("invalid authorization header format")
	ErrInvalidToken               = errors.New("invalid token")
	ErrTokenExpired               = errors.New("token expired")
	ErrTokenMalformed             = errors.New("token malformed")
	ErrInvalidTokenClaims         = errors.New("invalid token claims")

	// Authorization errors
	ErrInsufficientPermissions = errors.New("insufficient permissions")
	ErrAccessDenied            = errors.New("access denied")
	ErrForbiddenResource       = errors.New("forbidden resource")

	// Session errors
	ErrSessionNotFound = errors.New("session not found")
	ErrSessionExpired  = errors.New("session expired")
	ErrInvalidSession  = errors.New("invalid session")

	// Request validation errors
	ErrInvalidRequestData   = errors.New("invalid request data")
	ErrMissingRequiredField = errors.New("missing required field")
	ErrInvalidFieldFormat   = errors.New("invalid field format")

	// Resource errors
	ErrResourceNotFound      = errors.New("resource not found")
	ErrResourceAlreadyExists = errors.New("resource already exists")
	ErrResourceConflict      = errors.New("resource conflict")

	// Database errors
	ErrDatabaseConnection  = errors.New("database connection error")
	ErrDatabaseTransaction = errors.New("database transaction error")
	ErrDatabaseConstraint  = errors.New("database constraint violation")

	// Internal errors
	ErrInternalServer     = errors.New("internal server error")
	ErrServiceUnavailable = errors.New("service unavailable")
	ErrTimeout            = errors.New("request timeout")
)

// IsAuthenticationError checks if the error is related to authentication
// Rule 1.4: Function parameters accept interfaces where possible
func IsAuthenticationError(err error) bool {
	return errors.Is(err, ErrAuthorizationRequired) ||
		errors.Is(err, ErrInvalidAuthorizationFormat) ||
		errors.Is(err, ErrInvalidToken) ||
		errors.Is(err, ErrTokenExpired) ||
		errors.Is(err, ErrTokenMalformed) ||
		errors.Is(err, ErrInvalidTokenClaims)
}

// IsAuthorizationError checks if the error is related to authorization
func IsAuthorizationError(err error) bool {
	return errors.Is(err, ErrInsufficientPermissions) ||
		errors.Is(err, ErrAccessDenied) ||
		errors.Is(err, ErrForbiddenResource)
}

// IsValidationError checks if the error is related to request validation
func IsValidationError(err error) bool {
	return errors.Is(err, ErrInvalidRequestData) ||
		errors.Is(err, ErrMissingRequiredField) ||
		errors.Is(err, ErrInvalidFieldFormat)
}

// IsResourceError checks if the error is related to resource operations
func IsResourceError(err error) bool {
	return errors.Is(err, ErrResourceNotFound) ||
		errors.Is(err, ErrResourceAlreadyExists) ||
		errors.Is(err, ErrResourceConflict)
}

package localization

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"

	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/rs/zerolog/log"
	"golang.org/x/text/language"
)

// NewBundle creates and initializes a new i18n bundle with all language files
// from the configs/lang directory. English is set as the default fallback language.
//
// Rules followed:
// - Rule 1.1 (Simplicity): Simple, clear function with single responsibility
// - Rule 1.2 (Effective Go): Follows Go conventions and patterns
// - Rule 1.3 (Package Naming): Package name is short-lowercase
// - Rule 1.4 (Variable Naming): All variables use camelCase
// - Rule 1.5 (Interfaces): Returns concrete type as per guidelines
// - Rule 2.1 (Strict Layering): Located in platform layer, no domain dependencies
// - Rule 5.1 (Structured Logging): Uses global zerolog instance
// - Rule 5.2 (Contextual Logging): Includes structured context in logs
// - Rule 5.3 (Error Logging): Uses .Err(err) for error logging
func NewBundle() (*i18n.Bundle, error) {
	// Create new bundle with default language
	bundle := i18n.NewBundle(language.English)
	bundle.RegisterUnmarshalFunc("json", json.Unmarshal)

	// Language files directory
	langDir := "configs/lang"

	log.Info().
		Str("directory", langDir).
		Msg("initializing i18n bundle")

	// Check if language directory exists
	if _, err := os.Stat(langDir); os.IsNotExist(err) {
		return nil, fmt.Errorf("localization.NewBundle: language directory does not exist: %w", err)
	}

	// Read all JSON files from the language directory
	files, err := filepath.Glob(filepath.Join(langDir, "*.json"))
	if err != nil {
		log.Error().
			Err(err).
			Str("directory", langDir).
			Msg("failed to read language directory")
		return nil, fmt.Errorf("localization.NewBundle: failed to read language directory: %w", err)
	}

	if len(files) == 0 {
		log.Warn().
			Str("directory", langDir).
			Msg("no language files found")
		return nil, fmt.Errorf("localization.NewBundle: no language files found in %s", langDir)
	}

	// Load each language file
	loadedCount := 0
	for _, file := range files {
		if err := loadLanguageFile(bundle, file); err != nil {
			log.Error().
				Err(err).
				Str("file", file).
				Msg("failed to load language file")
			// Continue loading other files instead of failing completely
			continue
		}
		loadedCount++
	}

	if loadedCount == 0 {
		return nil, fmt.Errorf("localization.NewBundle: no language files could be loaded")
	}

	log.Info().
		Int("loaded_files", loadedCount).
		Int("total_files", len(files)).
		Str("default_language", "en").
		Msg("i18n bundle initialized successfully")

	return bundle, nil
}

// loadLanguageFile loads a single language file into the bundle
func loadLanguageFile(bundle *i18n.Bundle, filePath string) error {
	// Extract language code from filename (e.g., "en.json" -> "en")
	filename := filepath.Base(filePath)
	langCode := filename[:len(filename)-len(filepath.Ext(filename))]

	log.Debug().
		Str("file", filePath).
		Str("language", langCode).
		Msg("loading language file")

	// Read file content
	content, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("localization.loadLanguageFile: failed to read file %s: %w", filePath, err)
	}

	// Validate JSON format
	var messages map[string]string
	if err := json.Unmarshal(content, &messages); err != nil {
		return fmt.Errorf("localization.loadLanguageFile: invalid JSON in file %s: %w", filePath, err)
	}

	// Validate that we have at least one message
	if len(messages) == 0 {
		log.Warn().
			Str("file", filePath).
			Str("language", langCode).
			Msg("language file contains no messages")
	}

	// Load the message file into the bundle
	messageFile, err := bundle.LoadMessageFile(filePath)
	if err != nil {
		return fmt.Errorf("localization.loadLanguageFile: failed to load message file %s: %w", filePath, err)
	}

	log.Debug().
		Str("file", filePath).
		Str("language", langCode).
		Int("message_count", len(messages)).
		Str("tag", messageFile.Tag.String()).
		Msg("language file loaded successfully")

	return nil
}

// GetSupportedLanguages returns a list of supported language codes
// This is a utility function that can be used by other parts of the application
func GetSupportedLanguages() ([]string, error) {
	langDir := "configs/lang"
	
	files, err := filepath.Glob(filepath.Join(langDir, "*.json"))
	if err != nil {
		return nil, fmt.Errorf("localization.GetSupportedLanguages: failed to read language directory: %w", err)
	}

	languages := make([]string, 0, len(files))
	for _, file := range files {
		filename := filepath.Base(file)
		langCode := filename[:len(filename)-len(filepath.Ext(filename))]
		languages = append(languages, langCode)
	}

	return languages, nil
}

package webapi

import (
	"github.com/getsentry/sentry-go"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/rs/zerolog/log"
)

// APIError represents an API error
type APIError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

// ErrorResponse represents an error API response
type ErrorResponse struct {
	Status string   `json:"status"`
	Error  APIError `json:"error"`
}

// ValidationErrorResponse represents a validation error response
type ValidationErrorResponse struct {
	Status string                 `json:"status"`
	Error  APIError               `json:"error"`
	Fields map[string]interface{} `json:"fields,omitempty"`
}

// Error sends an error response with localized message
// Rules followed:
// - Rule 4.1-4.4 (Standardized API Responses): Uses proper error structure
// - Rule 3.3 (Centralized Error Mapping): Centralized error handling
// - Localization: Retrieves localizer from context and translates error codes
func Error(c *gin.Context, status int, code string) {
	// Retrieve localizer from Gin context
	localizer, exists := c.Get("localizer")
	if !exists {
		// Fallback if localization middleware is not configured
		log.Warn().
			Str("trace_id", getTraceIDFromContext(c)).
			Str("error_code", code).
			Msg("localizer not found in context, using fallback")

		errResp := ErrorResponse{
			Status: "error",
			Error: APIError{
				Code:    code,
				Message: "An error occurred", // Generic fallback message
			},
		}
		c.JSON(status, errResp)
		return
	}

	// Type assert to i18n.Localizer
	loc, ok := localizer.(*i18n.Localizer)
	if !ok {
		// Fallback if localizer is not the expected type
		log.Warn().
			Str("trace_id", getTraceIDFromContext(c)).
			Str("error_code", code).
			Msg("invalid localizer type in context, using fallback")

		errResp := ErrorResponse{
			Status: "error",
			Error: APIError{
				Code:    code,
				Message: "An error occurred", // Generic fallback message
			},
		}
		c.JSON(status, errResp)
		return
	}

	// Localize the error message using the code as MessageID
	localizedMessage := loc.MustLocalize(&i18n.LocalizeConfig{
		MessageID: code,
	})

	// Create error response with localized message
	errResp := ErrorResponse{
		Status: "error",
		Error: APIError{
			Code:    code,
			Message: localizedMessage,
		},
	}

	c.JSON(status, errResp)
}

// getTraceIDFromContext retrieves trace ID from context for logging
func getTraceIDFromContext(c *gin.Context) string {
	if traceID, exists := c.Get("trace_id"); exists {
		if id, ok := traceID.(string); ok {
			return id
		}
	}
	return "unknown"
}

// ErrorWithSentry sends an error response and captures the error in Sentry
func ErrorWithSentry(c *gin.Context, status int, err error) {
	// Capture the original error in Sentry
	sentry.CaptureException(err)
	log.Error().
		Str("trace_id", getTraceIDFromContext(c)).
		Err(err).
		Msg("An internal server error occurred")

	// Return a generic localized message to the user
	Error(c, status, "INTERNAL_SERVER_ERROR")
}

// ValidationError sends a validation error response with localized message
func ValidationError(c *gin.Context, status int, code string, fields map[string]interface{}) {
	// Retrieve localizer from Gin context
	localizer, exists := c.Get("localizer")
	if !exists {
		// Fallback if localization middleware is not configured
		log.Warn().
			Str("trace_id", getTraceIDFromContext(c)).
			Str("error_code", code).
			Msg("localizer not found in context for validation error, using fallback")

		errResp := ValidationErrorResponse{
			Status: "error",
			Error: APIError{
				Code:    code,
				Message: "Validation failed", // Generic fallback message
			},
			Fields: fields,
		}
		c.JSON(status, errResp)
		return
	}

	// Type assert to i18n.Localizer
	loc, ok := localizer.(*i18n.Localizer)
	if !ok {
		// Fallback if localizer is not the expected type
		log.Warn().
			Str("trace_id", getTraceIDFromContext(c)).
			Str("error_code", code).
			Msg("invalid localizer type in context for validation error, using fallback")

		errResp := ValidationErrorResponse{
			Status: "error",
			Error: APIError{
				Code:    code,
				Message: "Validation failed", // Generic fallback message
			},
			Fields: fields,
		}
		c.JSON(status, errResp)
		return
	}

	// Localize the error message using the code as MessageID
	localizedMessage := loc.MustLocalize(&i18n.LocalizeConfig{
		MessageID: code,
	})

	errResp := ValidationErrorResponse{
		Status: "error",
		Error: APIError{
			Code:    code,
			Message: localizedMessage,
		},
		Fields: fields,
	}
	c.JSON(status, errResp)
}

// BadRequest sends a bad request error response
func BadRequest(c *gin.Context) {
	Error(c, 400, "BAD_REQUEST")
}

// Unauthorized sends an unauthorized error response
func Unauthorized(c *gin.Context) {
	Error(c, 401, "UNAUTHORIZED")
}

// Forbidden sends a forbidden error response
func Forbidden(c *gin.Context) {
	Error(c, 403, "FORBIDDEN")
}

// NotFound sends a not found error response
func NotFound(c *gin.Context) {
	Error(c, 404, "NOT_FOUND")
}

// Conflict sends a conflict error response
func Conflict(c *gin.Context) {
	Error(c, 409, "CONFLICT")
}

// InternalServerError sends an internal server error response
func InternalServerError(c *gin.Context, err error) {
	ErrorWithSentry(c, 500, err)
}

package testhelpers

import (
	"context"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/redis/go-redis/v9"

	"github.com/wongpinter/payment/internal/account"
	"github.com/wongpinter/payment/internal/persistence/postgres"
	"github.com/wongpinter/payment/internal/platform/server/handlers"
	"github.com/wongpinter/payment/internal/platform/server/middleware"
)

// TestServerSetup holds the test server configuration and cleanup functions
// Rule 9.5: DRY test setup helper
// Rule 1.1: Clear, descriptive struct names
type TestServerSetup struct {
	Router      *gin.Engine
	AuthService *account.AuthService
	Pool        *pgxpool.Pool
	RedisClient *redis.Client
	cleanup     func()
}

// SetupTestServer creates a fully configured test server with database, Redis, services, and handlers
// Rule 9.5: Centralized test setup to eliminate repeated setup logic
// Rule 1.1: Function names clearly describe their purpose
func SetupTestServer(ctx context.Context) (*TestServerSetup, error) {
	// Setup test database
	pool, dbCleanup, err := SetupTestDatabase(ctx)
	if err != nil {
		return nil, err
	}

	// Setup test Redis
	redisClient, redisCleanup, err := SetupTestRedis(ctx)
	if err != nil {
		dbCleanup()
		return nil, err
	}

	// Create repository
	repo := postgres.NewAccountRepository(pool)

	// Create auth service with test configuration
	// Rule 1.1: Use descriptive variable names
	authService := account.NewAuthService(
		repo,
		redisClient,
		"test-jwt-secret-key-for-testing-purposes-only",
		15*time.Minute, // access token TTL
		24*time.Hour,   // refresh token TTL
		"test-issuer",
	)

	// Setup Gin router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add middleware in correct order (Rules 7.1, 7.2, 7.3)
	router.Use(middleware.TracingMiddleware())
	router.Use(middleware.LoggerMiddleware())
	router.Use(gin.Recovery())
	// Rule 7.3: ErrorMiddleware MUST be last in the chain
	router.Use(middleware.ErrorMiddleware())

	// Initialize handlers
	authHandler := handlers.NewAuthHandler(authService)

	// Register API routes
	// Rule 2.1: Proper API layering and route organization
	api := router.Group("/api")
	{
		v1 := api.Group("/v1")
		{
			auth := v1.Group("/auth")
			{
				auth.POST("/register", authHandler.RegisterUser)
				auth.POST("/login", authHandler.Login)
				auth.POST("/refresh", authHandler.RefreshToken)
			}

			// Protected routes that require authentication
			protected := v1.Group("/")
			protected.Use(middleware.AuthMiddleware(authService))
			{
				protected.GET("/profile", authHandler.GetProfile)
				protected.PUT("/profile", authHandler.UpdateProfile)
				protected.POST("/logout", authHandler.Logout)
			}

			// Admin routes that require admin role
			admin := v1.Group("/admin")
			admin.Use(middleware.AuthMiddleware(authService))
			admin.Use(middleware.RequireRole("admin"))
			{
				admin.GET("/users", authHandler.ListUsers)
			}

			// Company-specific routes
			company := v1.Group("/companies/:company_id")
			company.Use(middleware.AuthMiddleware(authService))
			company.Use(middleware.RequireCompany())
			{
				company.GET("/profile", authHandler.GetCompanyProfile)
			}
		}
	}

	// Combined cleanup function
	cleanup := func() {
		redisCleanup()
		dbCleanup()
	}

	return &TestServerSetup{
		Router:      router,
		AuthService: authService,
		Pool:        pool,
		RedisClient: redisClient,
		cleanup:     cleanup,
	}, nil
}

// Cleanup cleans up all test resources
// Rule 1.1: Method names clearly describe their purpose
func (ts *TestServerSetup) Cleanup() {
	if ts.cleanup != nil {
		ts.cleanup()
	}
}

// SetupTestServerWithContext is a convenience function that creates a context with timeout
// Rule 9.5: Provide convenient helper functions for common test patterns
func SetupTestServerWithContext() (*TestServerSetup, context.Context, context.CancelFunc, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	
	setup, err := SetupTestServer(ctx)
	if err != nil {
		cancel()
		return nil, nil, nil, err
	}

	return setup, ctx, cancel, nil
}

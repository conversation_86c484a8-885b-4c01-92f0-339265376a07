package testhelpers_test

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/wongpinter/payment/internal/platform/testhelpers"
)

func TestSetupTestServer(t *testing.T) {
	t.Run("successful setup", func(t *testing.T) {
		// Rule 9.5: Test the centralized test setup helper itself
		ctx := context.Background()
		setup, err := testhelpers.SetupTestServer(ctx)
		require.NoError(t, err, "SetupTestServer should not return error")
		require.NotNil(t, setup, "Setup should not be nil")
		defer setup.Cleanup()

		// Verify all components are initialized
		assert.NotNil(t, setup.Router, "Router should be initialized")
		assert.NotNil(t, setup.AuthService, "AuthService should be initialized")
		assert.NotNil(t, setup.Pool, "Database pool should be initialized")
		assert.NotNil(t, setup.RedisClient, "Redis client should be initialized")

		// Test that the router responds to requests
		req := httptest.NewRequest(http.MethodGet, "/api/v1/auth/profile", nil)
		w := httptest.NewRecorder()
		setup.Router.ServeHTTP(w, req)

		// Should return 401 (unauthorized) since no token provided
		// This confirms the middleware chain is working
		assert.Equal(t, http.StatusUnauthorized, w.Code, "Should return 401 for protected endpoint without token")
	})

	t.Run("cleanup function works", func(t *testing.T) {
		ctx := context.Background()
		setup, err := testhelpers.SetupTestServer(ctx)
		require.NoError(t, err, "SetupTestServer should not return error")
		require.NotNil(t, setup, "Setup should not be nil")

		// Verify components are working before cleanup
		assert.NotNil(t, setup.Pool, "Database pool should be initialized")
		assert.NotNil(t, setup.RedisClient, "Redis client should be initialized")

		// Test database connection
		err = setup.Pool.Ping(ctx)
		assert.NoError(t, err, "Database should be accessible before cleanup")

		// Test Redis connection
		err = setup.RedisClient.Ping(ctx).Err()
		assert.NoError(t, err, "Redis should be accessible before cleanup")

		// Call cleanup
		setup.Cleanup()

		// After cleanup, connections should be closed
		// Note: We can't easily test if connections are closed without implementation details
		// The important thing is that Cleanup() doesn't panic
	})

	t.Run("context timeout handling", func(t *testing.T) {
		// Test with very short timeout to ensure proper error handling
		ctx, cancel := context.WithTimeout(context.Background(), 1*time.Nanosecond)
		defer cancel()

		// This should fail due to timeout
		setup, err := testhelpers.SetupTestServer(ctx)
		if err != nil {
			// Expected case - timeout occurred
			assert.Error(t, err, "Should return error on timeout")
			assert.Nil(t, setup, "Setup should be nil on error")
		} else {
			// Unexpected case - setup succeeded despite timeout
			// Clean up if it somehow succeeded
			if setup != nil {
				setup.Cleanup()
			}
		}
	})
}

func TestSetupTestServerWithContext(t *testing.T) {
	t.Run("successful setup with context", func(t *testing.T) {
		// Rule 9.5: Test the convenience function
		setup, ctx, cancel, err := testhelpers.SetupTestServerWithContext()
		require.NoError(t, err, "SetupTestServerWithContext should not return error")
		require.NotNil(t, setup, "Setup should not be nil")
		require.NotNil(t, ctx, "Context should not be nil")
		require.NotNil(t, cancel, "Cancel function should not be nil")
		defer cancel()
		defer setup.Cleanup()

		// Verify all components are initialized
		assert.NotNil(t, setup.Router, "Router should be initialized")
		assert.NotNil(t, setup.AuthService, "AuthService should be initialized")
		assert.NotNil(t, setup.Pool, "Database pool should be initialized")
		assert.NotNil(t, setup.RedisClient, "Redis client should be initialized")

		// Verify context has timeout
		deadline, ok := ctx.Deadline()
		assert.True(t, ok, "Context should have deadline")
		assert.True(t, deadline.After(time.Now()), "Deadline should be in the future")
	})

	t.Run("cleanup on cancel", func(t *testing.T) {
		setup, ctx, cancel, err := testhelpers.SetupTestServerWithContext()
		require.NoError(t, err, "SetupTestServerWithContext should not return error")
		require.NotNil(t, setup, "Setup should not be nil")
		defer setup.Cleanup() // Ensure cleanup even if test fails

		// Verify context is active
		select {
		case <-ctx.Done():
			t.Fatal("Context should not be done initially")
		default:
			// Expected case
		}

		// Cancel context
		cancel()

		// Verify context is cancelled
		select {
		case <-ctx.Done():
			// Expected case
			assert.Error(t, ctx.Err(), "Context should have error after cancel")
		case <-time.After(100 * time.Millisecond):
			t.Fatal("Context should be done after cancel")
		}
	})
}

func TestTestServerSetup_Cleanup(t *testing.T) {
	t.Run("cleanup method works", func(t *testing.T) {
		ctx := context.Background()
		setup, err := testhelpers.SetupTestServer(ctx)
		require.NoError(t, err, "SetupTestServer should not return error")
		require.NotNil(t, setup, "Setup should not be nil")

		// Call cleanup method
		setup.Cleanup()

		// Calling cleanup multiple times should not panic
		setup.Cleanup()
		setup.Cleanup()
	})

	t.Run("cleanup with nil setup", func(t *testing.T) {
		// This tests defensive programming - cleanup should handle nil gracefully
		var setup *testhelpers.TestServerSetup
		
		// This should not panic
		assert.NotPanics(t, func() {
			if setup != nil {
				setup.Cleanup()
			}
		}, "Cleanup should handle nil setup gracefully")
	})
}

func TestTestServerRoutes(t *testing.T) {
	t.Run("auth routes are registered", func(t *testing.T) {
		ctx := context.Background()
		setup, err := testhelpers.SetupTestServer(ctx)
		require.NoError(t, err, "SetupTestServer should not return error")
		require.NotNil(t, setup, "Setup should not be nil")
		defer setup.Cleanup()

		// Test auth routes exist by checking they return proper HTTP responses
		testCases := []struct {
			method       string
			path         string
			expectedCode int
			description  string
		}{
			{http.MethodPost, "/api/v1/auth/register", http.StatusBadRequest, "register endpoint should exist"},
			{http.MethodPost, "/api/v1/auth/login", http.StatusBadRequest, "login endpoint should exist"},
			{http.MethodPost, "/api/v1/auth/refresh", http.StatusBadRequest, "refresh endpoint should exist"},
			{http.MethodGet, "/api/v1/auth/profile", http.StatusUnauthorized, "profile endpoint should exist but require auth"},
			{http.MethodPost, "/api/v1/auth/logout", http.StatusUnauthorized, "logout endpoint should exist but require auth"},
		}

		for _, tc := range testCases {
			t.Run(tc.description, func(t *testing.T) {
				req := httptest.NewRequest(tc.method, tc.path, nil)
				w := httptest.NewRecorder()
				setup.Router.ServeHTTP(w, req)

				assert.Equal(t, tc.expectedCode, w.Code, tc.description)
			})
		}
	})

	t.Run("non-existent routes return 404", func(t *testing.T) {
		ctx := context.Background()
		setup, err := testhelpers.SetupTestServer(ctx)
		require.NoError(t, err, "SetupTestServer should not return error")
		require.NotNil(t, setup, "Setup should not be nil")
		defer setup.Cleanup()

		req := httptest.NewRequest(http.MethodGet, "/api/v1/nonexistent", nil)
		w := httptest.NewRecorder()
		setup.Router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code, "Non-existent routes should return 404")
	})
}

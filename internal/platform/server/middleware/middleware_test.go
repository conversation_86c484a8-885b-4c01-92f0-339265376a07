package middleware

import (
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/wongpinter/payment/internal/account"
)

// TestErrorMiddleware tests the ErrorMiddleware functionality
// Rule 9.3: Comprehensive test coverage for middleware
func TestErrorMiddleware(t *testing.T) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name           string
		error          error
		expectedStatus int
		expectedCode   string
	}{
		{
			name:           "ErrAuthHeaderRequired",
			error:          ErrA<PERSON><PERSON>eaderRequired,
			expectedStatus: http.StatusUnauthorized,
			expectedCode:   "AUTHORIZATION_REQUIRED",
		},
		{
			name:           "ErrInvalidAuthFormat",
			error:          ErrInvalidAuthFormat,
			expectedStatus: http.StatusUnauthorized,
			expectedCode:   "INVALID_AUTHORIZATION_FORMAT",
		},
		{
			name:           "ErrInsufficientPermissions",
			error:          ErrInsufficientPermissions,
			expectedStatus: http.StatusForbidden,
			expectedCode:   "INSUFFICIENT_PERMISSIONS",
		},
		{
			name:           "ErrInvalidCredentials",
			error:          account.ErrInvalidCredentials,
			expectedStatus: http.StatusUnauthorized,
			expectedCode:   "INVALID_CREDENTIALS",
		},
		{
			name:           "ErrAccountNotFound",
			error:          account.ErrAccountNotFound,
			expectedStatus: http.StatusNotFound,
			expectedCode:   "ACCOUNT_NOT_FOUND",
		},
		{
			name:           "ErrAccountExists",
			error:          account.ErrAccountExists,
			expectedStatus: http.StatusConflict,
			expectedCode:   "ACCOUNT_ALREADY_EXISTS",
		},
		{
			name:           "ErrWeakPassword",
			error:          account.ErrWeakPassword,
			expectedStatus: http.StatusBadRequest,
			expectedCode:   "WEAK_PASSWORD",
		},
		{
			name:           "ErrInvalidAccountType",
			error:          account.ErrInvalidAccountType,
			expectedStatus: http.StatusBadRequest,
			expectedCode:   "INVALID_ACCOUNT_TYPE",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a new Gin router with error middleware
			router := gin.New()
			router.Use(ErrorMiddleware())

			// Create a test handler that returns the specific error
			router.GET("/test", func(c *gin.Context) {
				c.Error(tt.error)
			})

			// Create a test request
			req, err := http.NewRequest("GET", "/test", nil)
			require.NoError(t, err)

			// Create a response recorder
			w := httptest.NewRecorder()

			// Perform the request
			router.ServeHTTP(w, req)

			// Assert the response
			assert.Equal(t, tt.expectedStatus, w.Code, "Expected status code %d, got %d", tt.expectedStatus, w.Code)

			// Note: We can't easily test the exact JSON response structure here
			// because it requires the localization middleware to be set up.
			// The important part is that the correct status code is returned.
		})
	}
}

// TestErrorHelperFunctions tests the error classification helper functions
// Rule 9.3: Test all helper functions
func TestErrorHelperFunctions(t *testing.T) {
	t.Run("IsAuthenticationError", func(t *testing.T) {
		// Test positive cases
		assert.True(t, IsAuthenticationError(ErrAuthHeaderRequired))
		assert.True(t, IsAuthenticationError(ErrInvalidAuthFormat))
		assert.True(t, IsAuthenticationError(ErrInvalidToken))
		assert.True(t, IsAuthenticationError(ErrTokenExpired))
		assert.True(t, IsAuthenticationError(ErrTokenMalformed))
		assert.True(t, IsAuthenticationError(ErrInvalidTokenClaims))

		// Test negative cases
		assert.False(t, IsAuthenticationError(ErrInsufficientPermissions))
		assert.False(t, IsAuthenticationError(ErrAccessDenied))
		assert.False(t, IsAuthenticationError(errors.New("random error")))
	})

	t.Run("IsAuthorizationError", func(t *testing.T) {
		// Test positive cases
		assert.True(t, IsAuthorizationError(ErrInsufficientPermissions))
		assert.True(t, IsAuthorizationError(ErrAccessDenied))
		assert.True(t, IsAuthorizationError(ErrForbiddenResource))

		// Test negative cases
		assert.False(t, IsAuthorizationError(ErrAuthHeaderRequired))
		assert.False(t, IsAuthorizationError(ErrInvalidAuthFormat))
		assert.False(t, IsAuthorizationError(errors.New("random error")))
	})

	t.Run("IsCompanyError", func(t *testing.T) {
		// Test positive cases
		assert.True(t, IsCompanyError(ErrCompanyHeaderRequired))
		assert.True(t, IsCompanyError(ErrInvalidCompanyID))
		assert.True(t, IsCompanyError(ErrCompanyNotFound))
		assert.True(t, IsCompanyError(ErrUserNotInCompany))

		// Test negative cases
		assert.False(t, IsCompanyError(ErrAuthHeaderRequired))
		assert.False(t, IsCompanyError(ErrInsufficientPermissions))
		assert.False(t, IsCompanyError(errors.New("random error")))
	})

	t.Run("IsSessionError", func(t *testing.T) {
		// Test positive cases
		assert.True(t, IsSessionError(ErrSessionNotFound))
		assert.True(t, IsSessionError(ErrSessionExpired))
		assert.True(t, IsSessionError(ErrInvalidSession))

		// Test negative cases
		assert.False(t, IsSessionError(ErrAuthHeaderRequired))
		assert.False(t, IsSessionError(ErrInsufficientPermissions))
		assert.False(t, IsSessionError(errors.New("random error")))
	})

	t.Run("IsValidationError", func(t *testing.T) {
		// Test positive cases
		assert.True(t, IsValidationError(ErrInvalidRequestData))
		assert.True(t, IsValidationError(ErrMissingRequiredField))
		assert.True(t, IsValidationError(ErrInvalidFieldFormat))

		// Test negative cases
		assert.False(t, IsValidationError(ErrAuthHeaderRequired))
		assert.False(t, IsValidationError(ErrInsufficientPermissions))
		assert.False(t, IsValidationError(errors.New("random error")))
	})
}

// TestErrorVariables tests that all error variables are properly defined
// Rule 9.3: Test error variable definitions
func TestErrorVariables(t *testing.T) {
	// Test that all error variables are not nil and have proper messages
	errorTests := []struct {
		name  string
		err   error
		msg   string
	}{
		{"ErrAuthHeaderRequired", ErrAuthHeaderRequired, "authorization header required"},
		{"ErrInvalidAuthFormat", ErrInvalidAuthFormat, "invalid authorization header format"},
		{"ErrInvalidToken", ErrInvalidToken, "invalid token"},
		{"ErrTokenExpired", ErrTokenExpired, "token expired"},
		{"ErrTokenMalformed", ErrTokenMalformed, "token malformed"},
		{"ErrInvalidTokenClaims", ErrInvalidTokenClaims, "invalid token claims"},
		{"ErrInsufficientPermissions", ErrInsufficientPermissions, "insufficient permissions"},
		{"ErrAccessDenied", ErrAccessDenied, "access denied"},
		{"ErrForbiddenResource", ErrForbiddenResource, "forbidden resource"},
		{"ErrCompanyHeaderRequired", ErrCompanyHeaderRequired, "company header required"},
		{"ErrInvalidCompanyID", ErrInvalidCompanyID, "invalid company ID"},
		{"ErrCompanyNotFound", ErrCompanyNotFound, "company not found"},
		{"ErrUserNotInCompany", ErrUserNotInCompany, "user not in company"},
		{"ErrSessionNotFound", ErrSessionNotFound, "session not found"},
		{"ErrSessionExpired", ErrSessionExpired, "session expired"},
		{"ErrInvalidSession", ErrInvalidSession, "invalid session"},
		{"ErrInvalidRequestData", ErrInvalidRequestData, "invalid request data"},
		{"ErrMissingRequiredField", ErrMissingRequiredField, "missing required field"},
		{"ErrInvalidFieldFormat", ErrInvalidFieldFormat, "invalid field format"},
	}

	for _, tt := range errorTests {
		t.Run(tt.name, func(t *testing.T) {
			require.NotNil(t, tt.err, "Error variable should not be nil")
			assert.Equal(t, tt.msg, tt.err.Error(), "Error message should match expected")
		})
	}
}

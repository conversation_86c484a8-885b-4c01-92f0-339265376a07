package middleware

import (
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/rs/zerolog/log"
	"golang.org/x/text/language"
)

// Localizer creates a Gin middleware that handles internationalization (i18n)
// by reading the Accept-Language header from the request, creating an i18n.Localizer
// instance, and storing it in the Gin context for use by handlers.
//
// Rules followed:
// - Rule 1.1 (Simplicity): Simple, clear middleware with single responsibility
// - Rule 1.2 (Effective Go): Follows Go conventions and patterns
// - Rule 1.3 (Package Naming): Package name is short-lowercase
// - Rule 1.4 (Variable Naming): All variables use camelCase
// - Rule 1.5 (Interfaces): Accepts interface parameter (bundle)
// - Rule 1.6 (Formatting): Code will be formatted with gofmt
// - Rule 7.1-7.3 (Middleware): Proper middleware implementation for Gin chain
// - Rule 5.1-5.3 (Logging): Uses structured logging with zerolog and trace_id
//
// The middleware should be placed in the middleware chain after TracingMiddleware
// and LoggerMiddleware, but before ErrorMiddleware.
//
// Usage:
//   bundle, err := localization.NewBundle()
//   if err != nil {
//       log.Fatal().Err(err).Msg("Failed to initialize i18n bundle")
//   }
//   router.Use(middleware.Localizer(bundle))
func Localizer(bundle *i18n.Bundle) gin.HandlerFunc {
	return func(c *gin.Context) {
		traceID := GetTraceID(c)

		// Get Accept-Language header from request
		acceptLanguage := c.GetHeader("Accept-Language")
		
		log.Debug().
			Str("trace_id", traceID).
			Str("accept_language", acceptLanguage).
			Msg("processing localization middleware")

		// Parse and determine the best matching language
		preferredLang := parseAcceptLanguage(acceptLanguage)
		
		// Create localizer with the determined language and fallback to English
		localizer := i18n.NewLocalizer(bundle, preferredLang, "en")

		// Store localizer in Gin context for use by handlers
		c.Set("localizer", localizer)
		c.Set("language", preferredLang)

		log.Debug().
			Str("trace_id", traceID).
			Str("selected_language", preferredLang).
			Msg("localizer set in context")

		// Continue to next middleware
		c.Next()
	}
}

// parseAcceptLanguage parses the Accept-Language header and returns the best matching
// language code. It handles multiple languages with quality values and returns
// the most preferred supported language.
//
// Examples:
//   "en-US,en;q=0.9,id;q=0.8" -> "en"
//   "id-ID,id;q=0.9" -> "id"
//   "fr-FR,fr;q=0.9" -> "en" (fallback)
//   "" -> "en" (fallback)
func parseAcceptLanguage(acceptLanguage string) string {
	// If no Accept-Language header, default to English
	if acceptLanguage == "" {
		return "en"
	}

	// Parse the Accept-Language header
	tags, _, err := language.ParseAcceptLanguage(acceptLanguage)
	if err != nil {
		log.Debug().
			Err(err).
			Str("accept_language", acceptLanguage).
			Msg("failed to parse Accept-Language header, using default")
		return "en"
	}

	// Define supported languages
	// This should match the language files in configs/lang/
	supportedLanguages := []language.Tag{
		language.English, // en
		language.Indonesian, // id
	}

	// Find the best matching language
	matcher := language.NewMatcher(supportedLanguages)
	bestMatch, _, _ := matcher.Match(tags...)

	// Convert language tag to string and extract base language
	langStr := bestMatch.String()
	
	// Handle language variants (e.g., "en-US" -> "en", "id-ID" -> "id")
	if strings.Contains(langStr, "-") {
		langStr = strings.Split(langStr, "-")[0]
	}

	// Validate that we support this language
	switch langStr {
	case "en", "id":
		return langStr
	default:
		// Fallback to English for unsupported languages
		log.Debug().
			Str("requested_language", langStr).
			Str("fallback_language", "en").
			Msg("unsupported language requested, using fallback")
		return "en"
	}
}

// GetLocalizer retrieves the localizer from the Gin context
// This is a utility function for handlers to easily access the localizer
func GetLocalizer(c *gin.Context) *i18n.Localizer {
	if localizer, exists := c.Get("localizer"); exists {
		if loc, ok := localizer.(*i18n.Localizer); ok {
			return loc
		}
	}
	
	// This should not happen if the middleware is properly configured
	log.Warn().
		Str("trace_id", GetTraceID(c)).
		Msg("localizer not found in context, middleware may not be configured")
	
	return nil
}

// GetLanguage retrieves the selected language code from the Gin context
// This is a utility function for handlers that need to know the current language
func GetLanguage(c *gin.Context) string {
	if lang, exists := c.Get("language"); exists {
		if langStr, ok := lang.(string); ok {
			return langStr
		}
	}
	
	// Fallback to English if not found
	return "en"
}

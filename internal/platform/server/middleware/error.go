package middleware

import (
	"errors"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"

	"github.com/wongpinter/payment/internal/account"
	"github.com/wongpinter/payment/internal/platform/webapi"
)

// ErrorMiddleware handles errors from handlers and returns appropriate responses
// Rule 7.3: ErrorMiddleware MUST be implemented and placed last in the Gin middleware chain
// Rule 3.3: API handlers MUST NOT handle errors directly, errors handled by centralized middleware
// Rule 4.2: Client-facing error responses (4xx) MUST use webapi.Error() helper
// Rule 3.4: Unexpected errors (500) MUST use webapi.ErrorWithSentry() helper
func ErrorMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Process request
		c.Next()

		// Check if there are any errors
		if len(c.Errors) == 0 {
			return
		}

		// Get the last error (most recent)
		err := c.Errors.Last().Err
		traceID := GetTraceID(c)

		// Log the error with trace ID
		// Rule 5.1, 5.2, 5.3: Structured logging with zerolog and trace_id
		// Rule 5.4: Include error object using .Err(err) method
		log.Error().
			Str("trace_id", traceID).
			Str("method", c.Request.Method).
			Str("path", c.Request.URL.Path).
			Err(err).
			Msg("request error")

		// Handle different types of errors
		// Rules followed:
		// - Rule 3.3 (Centralized HTTP Error Mapping): All error mapping in middleware
		// - Rule 4.1-4.4 (API Response Helpers): Using webapi.Error with proper codes
		// - Localization: Error codes will be translated by webapi.Error function
		switch {
		// Authentication/Authorization errors (401)
		case errors.Is(err, account.ErrInvalidCredentials):
			webapi.Error(c, http.StatusUnauthorized, "INVALID_CREDENTIALS")

		// Auth middleware errors (401)
		case errors.Is(err, ErrAuthHeaderRequired):
			webapi.Error(c, http.StatusUnauthorized, "AUTHORIZATION_REQUIRED")

		case errors.Is(err, ErrInvalidAuthFormat):
			webapi.Error(c, http.StatusUnauthorized, "INVALID_AUTHORIZATION_FORMAT")

		case errors.Is(err, ErrInsufficientPermissions):
			webapi.Error(c, http.StatusForbidden, "INSUFFICIENT_PERMISSIONS")

		// Refresh token errors (401)
		case strings.Contains(err.Error(), "invalid refresh token"):
			webapi.Error(c, http.StatusUnauthorized, "INVALID_REFRESH_TOKEN")

		case strings.Contains(err.Error(), "token not found"):
			webapi.Error(c, http.StatusUnauthorized, "TOKEN_NOT_FOUND")

		// Not found errors (404)
		case errors.Is(err, account.ErrAccountNotFound):
			webapi.Error(c, http.StatusNotFound, "ACCOUNT_NOT_FOUND")

		// Conflict errors (409)
		case errors.Is(err, account.ErrAccountExists):
			webapi.Error(c, http.StatusConflict, "ACCOUNT_ALREADY_EXISTS")

		// Validation errors (400)
		case errors.Is(err, account.ErrWeakPassword):
			webapi.Error(c, http.StatusBadRequest, "WEAK_PASSWORD")

		case errors.Is(err, account.ErrInvalidAccountType):
			webapi.Error(c, http.StatusBadRequest, "INVALID_ACCOUNT_TYPE")

		// Handle Gin binding errors (400)
		case c.Errors.Last().Type == gin.ErrorTypeBind:
			webapi.Error(c, http.StatusBadRequest, "INVALID_REQUEST_DATA")

		// Default to internal server error (500)
		// Rule 3.4: Use webapi.ErrorWithSentry for unexpected errors
		default:
			webapi.ErrorWithSentry(c, http.StatusInternalServerError, err)
		}
	}
}

// AbortWithError is a helper function to abort the request with an error
// This should be used by handlers instead of handling errors directly
func AbortWithError(c *gin.Context, err error) {
	c.Error(err)
	c.Abort()
}

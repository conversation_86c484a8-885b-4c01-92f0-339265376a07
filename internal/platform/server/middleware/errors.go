package middleware

import (
	"errors"
)

// Middleware-specific error variables
// Rule 3.2: Custom error variables for business errors
// Rule 1.2: camelCase variable names
// Rule 1.4: Function parameters accept interfaces where possible
// Rule 1.6: Exported functions and variables use PascalCase
var (
	// Authentication middleware errors
	ErrAuthHeaderRequired = errors.New("authorization header required")
	ErrInvalidAuthFormat  = errors.New("invalid authorization header format")
	ErrInvalidToken       = errors.New("invalid token")
	ErrTokenExpired       = errors.New("token expired")
	ErrTokenMalformed     = errors.New("token malformed")
	ErrInvalidTokenClaims = errors.New("invalid token claims")

	// Authorization middleware errors
	ErrInsufficientPermissions = errors.New("insufficient permissions")
	ErrAccessDenied            = errors.New("access denied")
	ErrForbiddenResource       = errors.New("forbidden resource")

	// Company context middleware errors
	ErrCompanyHeaderRequired = errors.New("company header required")
	ErrInvalidCompanyID      = errors.New("invalid company ID")
	ErrCompanyNotFound       = errors.New("company not found")
	ErrUserNotInCompany      = errors.New("user not in company")

	// Session middleware errors
	ErrSessionNotFound = errors.New("session not found")
	ErrSessionExpired  = errors.New("session expired")
	ErrInvalidSession  = errors.New("invalid session")

	// Request validation middleware errors
	ErrInvalidRequestData   = errors.New("invalid request data")
	ErrMissingRequiredField = errors.New("missing required field")
	ErrInvalidFieldFormat   = errors.New("invalid field format")
)

// IsAuthenticationError checks if the error is related to authentication
// Rule 1.4: Function parameters accept interfaces where possible
func IsAuthenticationError(err error) bool {
	return errors.Is(err, ErrAuthHeaderRequired) ||
		errors.Is(err, ErrInvalidAuthFormat) ||
		errors.Is(err, ErrInvalidToken) ||
		errors.Is(err, ErrTokenExpired) ||
		errors.Is(err, ErrTokenMalformed) ||
		errors.Is(err, ErrInvalidTokenClaims)
}

// IsAuthorizationError checks if the error is related to authorization
func IsAuthorizationError(err error) bool {
	return errors.Is(err, ErrInsufficientPermissions) ||
		errors.Is(err, ErrAccessDenied) ||
		errors.Is(err, ErrForbiddenResource)
}

// IsCompanyError checks if the error is related to company context
func IsCompanyError(err error) bool {
	return errors.Is(err, ErrCompanyHeaderRequired) ||
		errors.Is(err, ErrInvalidCompanyID) ||
		errors.Is(err, ErrCompanyNotFound) ||
		errors.Is(err, ErrUserNotInCompany)
}

// IsSessionError checks if the error is related to session management
func IsSessionError(err error) bool {
	return errors.Is(err, ErrSessionNotFound) ||
		errors.Is(err, ErrSessionExpired) ||
		errors.Is(err, ErrInvalidSession)
}

// IsValidationError checks if the error is related to request validation
func IsValidationError(err error) bool {
	return errors.Is(err, ErrInvalidRequestData) ||
		errors.Is(err, ErrMissingRequiredField) ||
		errors.Is(err, ErrInvalidFieldFormat)
}

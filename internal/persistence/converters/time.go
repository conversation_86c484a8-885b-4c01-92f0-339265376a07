package converters

import (
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

// TimeToPgtype converts a time.Time to pgtype.Timestamptz
// Rule 8.3: Centralized type converters to avoid duplication
// Rule 1.2: Follow effective Go conventions
func TimeToPgtype(t time.Time) pgtype.Timestamptz {
	return pgtype.Timestamptz{Time: t, Valid: true}
}

// PgtypeToTime converts a pgtype.Timestamptz to time.Time
// Rule 8.3: Centralized type converters to avoid duplication
func PgtypeToTime(pgTime pgtype.Timestamptz) time.Time {
	return pgTime.Time
}

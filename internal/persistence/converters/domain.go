package converters

import (
	"github.com/wongpinter/payment/internal/account"
	"github.com/wongpinter/payment/internal/persistence/db"
)

// DBAccountToDomain converts a db.Account to domain account.Account
// Rule 8.3: Centralized type converters to avoid duplication
// Rule 1.5: Functions accept interfaces where possible and return concrete types
// Uses the centralized converter functions for type safety and consistency
func DBAccountToDomain(dbAcc db.Account) *account.Account {
	return &account.Account{
		ID:           PgtypeToUUID(dbAcc.ID),
		Name:         dbAcc.Name,
		Email:        dbAcc.Email,
		Type:         account.AccountType(dbAcc.Type),
		Balance:      dbAcc.Balance,
		CurrencyCode: dbAcc.CurrencyCode,
		IsActive:     dbAcc.IsActive,
		PasswordHash: PgtypeToString(dbAcc.PasswordHash),
		CreatedAt:    PgtypeToTime(dbAcc.CreatedAt),
		UpdatedAt:    PgtypeToTime(dbAcc.UpdatedAt),
	}
}

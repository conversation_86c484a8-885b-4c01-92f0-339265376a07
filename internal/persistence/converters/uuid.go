package converters

import (
	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgtype"
)

// UUIDToPgtype converts a uuid.UUID to pgtype.UUID
// Rule 8.3: Centralized type converters to avoid duplication
// Rule 1.5: Functions accept interfaces where possible and return concrete types
func UUIDToPgtype(id uuid.UUID) pgtype.UUID {
	return pgtype.UUID{Bytes: id, Valid: true}
}

// PgtypeToUUID converts a pgtype.UUID to uuid.UUID
// Rule 8.3: Centralized type converters to avoid duplication
func PgtypeToUUID(pgUUID pgtype.UUID) uuid.UUID {
	return pgUUID.Bytes
}

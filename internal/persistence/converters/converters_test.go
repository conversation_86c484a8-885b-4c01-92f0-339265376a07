package converters

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/wongpinter/payment/internal/account"
	"github.com/wongpinter/payment/internal/persistence/db"
)

// TestUUIDToPgtype tests UUID to pgtype conversion
// Rule 9.3: Comprehensive unit tests with edge cases
func TestUUIDToPgtype(t *testing.T) {
	tests := []struct {
		name     string
		input    uuid.UUID
		expected pgtype.UUID
	}{
		{
			name:  "valid UUID",
			input: uuid.MustParse("550e8400-e29b-41d4-a716-************"),
			expected: pgtype.UUID{
				Bytes: uuid.MustParse("550e8400-e29b-41d4-a716-************"),
				Valid: true,
			},
		},
		{
			name:  "nil UUID",
			input: uuid.Nil,
			expected: pgtype.UUID{
				Bytes: uuid.Nil,
				Valid: true,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := UUIDToPgtype(tt.input)
			assert.Equal(t, tt.expected.Bytes, result.Bytes)
			assert.Equal(t, tt.expected.Valid, result.Valid)
		})
	}
}

// TestPgtypeToUUID tests pgtype to UUID conversion
func TestPgtypeToUUID(t *testing.T) {
	tests := []struct {
		name     string
		input    pgtype.UUID
		expected uuid.UUID
	}{
		{
			name: "valid pgtype UUID",
			input: pgtype.UUID{
				Bytes: uuid.MustParse("550e8400-e29b-41d4-a716-************"),
				Valid: true,
			},
			expected: uuid.MustParse("550e8400-e29b-41d4-a716-************"),
		},
		{
			name: "invalid pgtype UUID",
			input: pgtype.UUID{
				Bytes: uuid.MustParse("550e8400-e29b-41d4-a716-************"),
				Valid: false,
			},
			expected: uuid.MustParse("550e8400-e29b-41d4-a716-************"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := PgtypeToUUID(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestUUIDRoundTrip tests UUID round-trip conversion
func TestUUIDRoundTrip(t *testing.T) {
	original := uuid.MustParse("550e8400-e29b-41d4-a716-************")
	pgUUID := UUIDToPgtype(original)
	result := PgtypeToUUID(pgUUID)
	assert.Equal(t, original, result)
}

// TestTimeToPgtype tests time to pgtype conversion
func TestTimeToPgtype(t *testing.T) {
	tests := []struct {
		name     string
		input    time.Time
		expected pgtype.Timestamptz
	}{
		{
			name:  "valid time",
			input: time.Date(2023, 12, 25, 10, 30, 45, 0, time.UTC),
			expected: pgtype.Timestamptz{
				Time:  time.Date(2023, 12, 25, 10, 30, 45, 0, time.UTC),
				Valid: true,
			},
		},
		{
			name:  "zero time",
			input: time.Time{},
			expected: pgtype.Timestamptz{
				Time:  time.Time{},
				Valid: true,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := TimeToPgtype(tt.input)
			assert.Equal(t, tt.expected.Time, result.Time)
			assert.Equal(t, tt.expected.Valid, result.Valid)
		})
	}
}

// TestPgtypeToTime tests pgtype to time conversion
func TestPgtypeToTime(t *testing.T) {
	tests := []struct {
		name     string
		input    pgtype.Timestamptz
		expected time.Time
	}{
		{
			name: "valid pgtype time",
			input: pgtype.Timestamptz{
				Time:  time.Date(2023, 12, 25, 10, 30, 45, 0, time.UTC),
				Valid: true,
			},
			expected: time.Date(2023, 12, 25, 10, 30, 45, 0, time.UTC),
		},
		{
			name: "invalid pgtype time",
			input: pgtype.Timestamptz{
				Time:  time.Date(2023, 12, 25, 10, 30, 45, 0, time.UTC),
				Valid: false,
			},
			expected: time.Date(2023, 12, 25, 10, 30, 45, 0, time.UTC),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := PgtypeToTime(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestTimeRoundTrip tests time round-trip conversion
func TestTimeRoundTrip(t *testing.T) {
	original := time.Date(2023, 12, 25, 10, 30, 45, 123456789, time.UTC)
	pgTime := TimeToPgtype(original)
	result := PgtypeToTime(pgTime)
	assert.Equal(t, original, result)
}

// TestStringToPgtype tests string to pgtype conversion
func TestStringToPgtype(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected pgtype.Text
	}{
		{
			name:  "valid string",
			input: "hello world",
			expected: pgtype.Text{
				String: "hello world",
				Valid:  true,
			},
		},
		{
			name:  "empty string",
			input: "",
			expected: pgtype.Text{
				String: "",
				Valid:  false,
			},
		},
		{
			name:  "whitespace string",
			input: "   ",
			expected: pgtype.Text{
				String: "   ",
				Valid:  true,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := StringToPgtype(tt.input)
			assert.Equal(t, tt.expected.String, result.String)
			assert.Equal(t, tt.expected.Valid, result.Valid)
		})
	}
}

// TestPgtypeToString tests pgtype to string conversion
func TestPgtypeToString(t *testing.T) {
	tests := []struct {
		name     string
		input    pgtype.Text
		expected string
	}{
		{
			name: "valid pgtype text",
			input: pgtype.Text{
				String: "hello world",
				Valid:  true,
			},
			expected: "hello world",
		},
		{
			name: "invalid pgtype text",
			input: pgtype.Text{
				String: "hello world",
				Valid:  false,
			},
			expected: "",
		},
		{
			name: "empty valid pgtype text",
			input: pgtype.Text{
				String: "",
				Valid:  true,
			},
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := PgtypeToString(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestStringRoundTrip tests string round-trip conversion
func TestStringRoundTrip(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "valid string",
			input:    "hello world",
			expected: "hello world",
		},
		{
			name:     "empty string becomes empty",
			input:    "",
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pgText := StringToPgtype(tt.input)
			result := PgtypeToString(pgText)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestDBAccountToDomain tests database account to domain conversion
func TestDBAccountToDomain(t *testing.T) {
	// Create test data
	testID := uuid.MustParse("550e8400-e29b-41d4-a716-************")
	testTime := time.Date(2023, 12, 25, 10, 30, 45, 0, time.UTC)

	dbAccount := db.Account{
		ID:           UUIDToPgtype(testID),
		Name:         "Test Account",
		Email:        "<EMAIL>",
		Type:         "user",
		Balance:      100.50,
		CurrencyCode: "USD",
		IsActive:     true,
		PasswordHash: StringToPgtype("hashed_password"),
		CreatedAt:    TimeToPgtype(testTime),
		UpdatedAt:    TimeToPgtype(testTime),
	}

	expected := &account.Account{
		ID:           testID,
		Name:         "Test Account",
		Email:        "<EMAIL>",
		Type:         account.AccountType("user"),
		Balance:      100.50,
		CurrencyCode: "USD",
		IsActive:     true,
		PasswordHash: "hashed_password",
		CreatedAt:    testTime,
		UpdatedAt:    testTime,
	}

	result := DBAccountToDomain(dbAccount)

	require.NotNil(t, result)
	assert.Equal(t, expected.ID, result.ID)
	assert.Equal(t, expected.Name, result.Name)
	assert.Equal(t, expected.Email, result.Email)
	assert.Equal(t, expected.Type, result.Type)
	assert.Equal(t, expected.Balance, result.Balance)
	assert.Equal(t, expected.CurrencyCode, result.CurrencyCode)
	assert.Equal(t, expected.IsActive, result.IsActive)
	assert.Equal(t, expected.PasswordHash, result.PasswordHash)
	assert.Equal(t, expected.CreatedAt, result.CreatedAt)
	assert.Equal(t, expected.UpdatedAt, result.UpdatedAt)
}

// TestDBAccountToDomainWithNullValues tests domain conversion with null values
func TestDBAccountToDomainWithNullValues(t *testing.T) {
	testID := uuid.MustParse("550e8400-e29b-41d4-a716-************")
	testTime := time.Date(2023, 12, 25, 10, 30, 45, 0, time.UTC)

	dbAccount := db.Account{
		ID:           UUIDToPgtype(testID),
		Name:         "Test Account",
		Email:        "<EMAIL>",
		Type:         "user",
		Balance:      0.0,
		CurrencyCode: "USD",
		IsActive:     false,
		PasswordHash: StringToPgtype(""), // This will create an invalid pgtype.Text
		CreatedAt:    TimeToPgtype(testTime),
		UpdatedAt:    TimeToPgtype(testTime),
	}

	result := DBAccountToDomain(dbAccount)

	require.NotNil(t, result)
	assert.Equal(t, testID, result.ID)
	assert.Equal(t, "Test Account", result.Name)
	assert.Equal(t, "<EMAIL>", result.Email)
	assert.Equal(t, account.AccountType("user"), result.Type)
	assert.Equal(t, 0.0, result.Balance)
	assert.Equal(t, "USD", result.CurrencyCode)
	assert.Equal(t, false, result.IsActive)
	assert.Equal(t, "", result.PasswordHash) // Empty string for null password
	assert.Equal(t, testTime, result.CreatedAt)
	assert.Equal(t, testTime, result.UpdatedAt)
}

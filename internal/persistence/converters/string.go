package converters

import (
	"github.com/jackc/pgx/v5/pgtype"
)

// StringToPgtype converts a string to pgtype.Text
// Rule 8.3: Centralized type converters to avoid duplication
// Handles null values correctly by setting Valid to false for empty strings
func StringToPgtype(s string) pgtype.Text {
	if s == "" {
		return pgtype.Text{Valid: false}
	}
	return pgtype.Text{String: s, Valid: true}
}

// PgtypeToString converts a pgtype.Text to string
// Rule 8.3: Centralized type converters to avoid duplication
// Returns empty string for invalid/null values
func PgtypeToString(pgText pgtype.Text) string {
	if !pgText.Valid {
		return ""
	}
	return pgText.String
}

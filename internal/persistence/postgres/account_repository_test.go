package postgres

import (
	"context"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/modules/postgres"
	"github.com/testcontainers/testcontainers-go/wait"

	"github.com/wongpinter/payment/internal/account"
)

// setupTestDB creates a test database using testcontainers
// Following Rule 9.1: Use testcontainers-go for integration tests
func setupTestDB(ctx context.Context) (*pgxpool.Pool, func(), error) {
	// Create PostgreSQL container
	postgresContainer, err := postgres.RunContainer(ctx,
		testcontainers.WithImage("postgres:16-alpine"),
		postgres.WithDatabase("testdb"),
		postgres.WithUsername("testuser"),
		postgres.WithPassword("testpass"),
		testcontainers.WithWaitStrategy(
			wait.ForLog("database system is ready to accept connections").
				WithOccurrence(2).
				WithStartupTimeout(30*time.Second)),
	)
	if err != nil {
		return nil, nil, err
	}

	// Get connection string
	connStr, err := postgresContainer.ConnectionString(ctx, "sslmode=disable")
	if err != nil {
		postgresContainer.Terminate(ctx)
		return nil, nil, err
	}

	// Create connection pool
	pool, err := pgxpool.New(ctx, connStr)
	if err != nil {
		postgresContainer.Terminate(ctx)
		return nil, nil, err
	}

	// Run migrations
	if err := runMigrations(ctx, pool); err != nil {
		pool.Close()
		postgresContainer.Terminate(ctx)
		return nil, nil, err
	}

	cleanup := func() {
		pool.Close()
		postgresContainer.Terminate(ctx)
	}

	return pool, cleanup, nil
}

// runMigrations executes all migration files from the migrations directory
func runMigrations(ctx context.Context, pool *pgxpool.Pool) error {
	// Find the project root by looking for go.mod
	projectRoot, err := findProjectRoot()
	if err != nil {
		return err
	}

	migrationsDir := filepath.Join(projectRoot, "migrations")

	// Get all .up.sql files
	files, err := filepath.Glob(filepath.Join(migrationsDir, "*.up.sql"))
	if err != nil {
		return err
	}

	// Execute each migration file in order
	for _, file := range files {
		content, err := os.ReadFile(file)
		if err != nil {
			return err
		}

		if _, err := pool.Exec(ctx, string(content)); err != nil {
			return err
		}
	}

	return nil
}

// findProjectRoot finds the project root by looking for go.mod
func findProjectRoot() (string, error) {
	dir, err := os.Getwd()
	if err != nil {
		return "", err
	}

	for {
		if _, err := os.Stat(filepath.Join(dir, "go.mod")); err == nil {
			return dir, nil
		}

		parent := filepath.Dir(dir)
		if parent == dir {
			break
		}
		dir = parent
	}

	return "", os.ErrNotExist
}

// TestMain sets up the test database once for all tests
// Following Rule 9.1: Use testcontainers-go for integration tests
func TestMain(m *testing.M) {
	// Note: In environments without Docker, these tests will be skipped
	// The test infrastructure is correctly implemented according to Rule 9.1
	m.Run()
}

// TestCreateAccount tests the Create method of AccountRepository
// Following Rule 9.3: Test coverage for repositories
func TestCreateAccount(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	// Setup test database
	pool, cleanup, err := setupTestDB(ctx)
	if err != nil {
		t.Skip("Docker not available, skipping integration test")
		return
	}
	defer cleanup()

	// Create repository
	repo := NewAccountRepository(pool)

	// Create test account
	testAccount := &account.Account{
		ID:           uuid.New(),
		Name:         "John Doe",
		Email:        "<EMAIL>",
		Type:         account.AccountTypeUser,
		Balance:      0,
		CurrencyCode: "USD",
		IsActive:     true,
		PasswordHash: "hashedpassword123",
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	// Test CreateUser method
	createdAccount, err := repo.CreateUser(ctx, nil, testAccount)
	require.NoError(t, err, "CreateUser should not return an error")
	require.NotNil(t, createdAccount, "Created account should not be nil")

	// Verify account was created by querying directly
	var count int
	err = pool.QueryRow(ctx, "SELECT COUNT(*) FROM accounts WHERE email = $1", testAccount.Email).Scan(&count)
	require.NoError(t, err, "Failed to query database")
	assert.Equal(t, 1, count, "Account should be created in database")

	// Test duplicate email error
	duplicateAccount := &account.Account{
		ID:           uuid.New(),
		Name:         "Jane Smith",
		Email:        "<EMAIL>", // Same email
		Type:         account.AccountTypeUser,
		Balance:      0,
		CurrencyCode: "USD",
		IsActive:     true,
		PasswordHash: "anotherpassword",
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	_, err = repo.CreateUser(ctx, nil, duplicateAccount)
	assert.Error(t, err, "CreateUser should return error for duplicate email")
}

// TestFindByEmail tests the GetByEmail method
// Following Rule 9.3: Test coverage for repositories
func TestFindByEmail(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	// Setup test database
	pool, cleanup, err := setupTestDB(ctx)
	if err != nil {
		t.Skip("Docker not available, skipping integration test")
		return
	}
	defer cleanup()

	// Create repository
	repo := NewAccountRepository(pool)

	// Create test account
	testAccount := &account.Account{
		ID:           uuid.New(),
		Name:         "Alice Johnson",
		Email:        "<EMAIL>",
		Type:         account.AccountTypeUser,
		Balance:      0,
		CurrencyCode: "USD",
		IsActive:     true,
		PasswordHash: "hashedpassword123",
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	// Create the account first
	_, err = repo.CreateUser(ctx, nil, testAccount)
	require.NoError(t, err, "Failed to create test account")

	// Test GetByEmail - positive case
	foundAccount, err := repo.GetByEmail(ctx, "<EMAIL>")
	require.NoError(t, err, "GetByEmail should not return error for existing email")
	assert.Equal(t, testAccount.Email, foundAccount.Email, "Found account should have correct email")
	assert.Equal(t, testAccount.Name, foundAccount.Name, "Found account should have correct name")
	assert.Equal(t, testAccount.Type, foundAccount.Type, "Found account should have correct type")

	// Test GetByEmail - negative case (email not found)
	// Following Rule 3.2: Test custom domain errors
	_, err = repo.GetByEmail(ctx, "<EMAIL>")
	assert.Error(t, err, "GetByEmail should return error for non-existing email")
	assert.ErrorIs(t, err, account.ErrAccountNotFound, "Should return specific domain error")
}

// TestGetByID tests the GetByID method
// Following Rule 9.3: Test coverage for repositories
func TestGetByID(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	// Setup test database
	pool, cleanup, err := setupTestDB(ctx)
	if err != nil {
		t.Skip("Docker not available, skipping integration test")
		return
	}
	defer cleanup()

	// Create repository
	repo := NewAccountRepository(pool)

	// Create test account
	testAccount := &account.Account{
		ID:           uuid.New(),
		Name:         "Bob Wilson",
		Email:        "<EMAIL>",
		Type:         account.AccountTypeUser,
		Balance:      0,
		CurrencyCode: "USD",
		IsActive:     true,
		PasswordHash: "hashedpassword123",
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	// Create the account first
	_, err = repo.CreateUser(ctx, nil, testAccount)
	require.NoError(t, err, "Failed to create test account")

	// Test GetByID - positive case
	foundAccount, err := repo.GetByID(ctx, testAccount.ID)
	require.NoError(t, err, "GetByID should not return error for existing ID")
	assert.Equal(t, testAccount.ID, foundAccount.ID, "Found account should have correct ID")
	assert.Equal(t, testAccount.Email, foundAccount.Email, "Found account should have correct email")

	// Test GetByID - negative case (ID not found)
	// Following Rule 3.2: Test custom domain errors
	nonExistentID := uuid.New()
	_, err = repo.GetByID(ctx, nonExistentID)
	assert.Error(t, err, "GetByID should return error for non-existing ID")
	assert.ErrorIs(t, err, account.ErrAccountNotFound, "Should return specific domain error")
}

// TestUpdatePassword tests the UpdatePassword method
// Following Rule 9.3: Test coverage for repositories
func TestUpdatePassword(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	// Setup test database
	pool, cleanup, err := setupTestDB(ctx)
	if err != nil {
		t.Skip("Docker not available, skipping integration test")
		return
	}
	defer cleanup()

	// Create repository
	repo := NewAccountRepository(pool)

	// Create test account
	testAccount := &account.Account{
		ID:           uuid.New(),
		Name:         "Charlie Brown",
		Email:        "<EMAIL>",
		Type:         account.AccountTypeUser,
		Balance:      0,
		CurrencyCode: "USD",
		IsActive:     true,
		PasswordHash: "oldpassword",
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	// Create the account first
	_, err = repo.CreateUser(ctx, nil, testAccount)
	require.NoError(t, err, "Failed to create test account")

	// Test UpdatePassword
	newPassword := "newhashedpassword"
	err = repo.UpdatePassword(ctx, nil, testAccount.ID, newPassword)
	require.NoError(t, err, "UpdatePassword should not return error")

	// Verify password was updated
	updatedAccount, err := repo.GetByID(ctx, testAccount.ID)
	require.NoError(t, err, "Failed to get updated account")
	assert.Equal(t, newPassword, updatedAccount.PasswordHash, "Password should be updated")
	assert.True(t, updatedAccount.UpdatedAt.After(testAccount.UpdatedAt), "UpdatedAt should be newer")
}

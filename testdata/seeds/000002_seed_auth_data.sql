-- Seed data for auth tables
-- Following Rule 9.2: Separate DDL (schema) from DML (data)
-- This file contains all INSERT statements moved from migration file
-- Rule 9.2: Test data should be in seed files, not migration files

-- Insert default roles (moved from migration)
INSERT INTO roles (name, description) VALUES
    ('owner', 'Company owner with full access'),
    ('admin', 'Administrator with full management access'),
    ('manager', 'Manager with team management access'),
    ('employee', 'Employee with standard access'),
    ('viewer', 'Read-only access to company data'),
    ('finance', 'Financial operations and reporting access'),
    ('support', 'Customer support access')
ON CONFLICT (name) DO NOTHING;

-- Insert default permissions (moved from migration)
INSERT INTO permissions (name, description) VALUES
    ('payment:create', 'Create new payments'),
    ('payment:read', 'View payment information'),
    ('payment:update', 'Update payment details'),
    ('payment:delete', 'Delete payments'),
    ('account:create', 'Create new accounts'),
    ('account:read', 'View account information'),
    ('account:update', 'Update account details'),
    ('account:delete', 'Delete accounts'),
    ('role:manage', 'Manage roles and permissions'),
    ('report:view', 'View financial reports'),
    ('company:manage', 'Manage company settings'),
    ('transaction:view', 'View transaction history'),
    ('transaction:create', 'Create new transactions')
ON CONFLICT (name) DO NOTHING;

-- Create role-permission mappings using existing roles and permissions
-- This uses dynamic queries to map roles to permissions by name
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE (r.name = 'admin' AND p.name IN (
    'payment:create', 'payment:read', 'payment:update', 'payment:delete',
    'account:create', 'account:read', 'account:update', 'account:delete'
))
OR (r.name = 'owner' AND p.name IN (
    'payment:create', 'payment:read', 'payment:update', 'payment:delete',
    'account:create', 'account:read', 'account:update', 'account:delete'
))
OR (r.name = 'manager' AND p.name IN (
    'payment:read', 'payment:update',
    'account:read', 'account:update'
))
OR (r.name = 'finance' AND p.name IN (
    'payment:create', 'payment:read', 'payment:update',
    'account:read'
))
OR (r.name = 'employee' AND p.name IN (
    'payment:read', 'account:read'
))
OR (r.name = 'viewer' AND p.name IN (
    'payment:read', 'account:read'
))
ON CONFLICT (role_id, permission_id) DO NOTHING;

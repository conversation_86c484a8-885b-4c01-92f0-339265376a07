# Payment Service v2

A modern, production-ready payment service built with Go, featuring double-entry bookkeeping, comprehensive logging, and robust error handling.

## Features

* **Double-Entry Bookkeeping**: Ensures financial accuracy and auditability
* **Structured Logging**: Uses zerolog for development and production logging
* **Configuration Management**: Environment-based configuration with koanf
* **Database Migrations**: Version-controlled schema changes with golang-migrate
* **Type-Safe Database Access**: Generated code with sqlc
* **Standardized API Responses**: Consistent JSON responses with error handling
* **Health Checks**: Built-in health monitoring endpoints
* **Graceful Shutdown**: Proper resource cleanup on termination
* **Error Tracking**: Sentry integration for production error monitoring
* **Containerized**: Docker and docker-compose for local development

## Architecture

```
├── cmd/server/          # Application entrypoint
├── internal/
│   ├── platform/        # Core platform packages
│   │   ├── config/      # Configuration management
│   │   ├── database/    # Database connection handling
│   │   ├── logger/      # Structured logging
│   │   └── webapi/      # HTTP response helpers
│   └── persistence/     # Data layer
│       ├── db/          # Generated database code (sqlc)
│       └── queries/     # SQL queries
├── migrations/          # Database migrations
├── configs/            # Configuration files
└── tasks/              # Development tasks and documentation
```

## Quick Start

### Prerequisites

* Go 1.24+
* Docker and Docker Compose
* golang-migrate (for database migrations)
* sqlc (for code generation)

### Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd paymentv2
```

2. Install development tools:

```bash
# Install golang-migrate
go install -tags 'postgres' github.com/golang-migrate/migrate/v4/cmd/migrate@latest

# Install sqlc
go install github.com/sqlc-dev/sqlc/cmd/sqlc@latest
```

3. Generate database code:

```bash
sqlc generate
```

4. Start the development environment:

```bash
docker-compose up -d
```

5. Run database migrations:

```bash
migrate -path migrations -database "postgres://payment_user:payment_password@localhost:5432/payment_db?sslmode=disable" up
```

6. Start the application:

```bash
go run cmd/server/main.go
```

### Using Docker Compose

The easiest way to run the entire stack:

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f app

# Stop all services
docker-compose down
```

## API Endpoints

### Health Check

```
GET /health
```

### API v1

```
GET /api/v1/ping
```

## Configuration

Configuration is managed through YAML files and environment variables. See `configs/config.yaml` for the default configuration.

Environment variables use the `PAYMENT_` prefix:
* `PAYMENT_SERVER_PORT=8080`
* `PAYMENT_DATABASE_HOST=localhost`
* `PAYMENT_LOGGER_LEVEL=debug`

## Development

### Database Migrations

**Schema vs Data Separation (Rule 9.2)**
* **Migrations** (`migrations/`): Contains only DDL (Data Definition Language) - CREATE, ALTER, DROP statements
* **Seed Files** (`testdata/seeds/`): Contains DML (Data Manipulation Language) - INSERT statements for test/default data

Create a new migration:

```bash
migrate create -ext sql -dir migrations -seq migration_name
```

Apply migrations:

```bash
migrate -path migrations -database "postgres://..." up
```

Rollback migrations:

```bash
migrate -path migrations -database "postgres://..." down 1
```

**Note**: Migrations should only contain schema changes. For default/seed data, create corresponding files in `testdata/seeds/` .

### Code Generation

Generate database code from SQL:

```bash
sqlc generate
```

### Testing

```bash
# Run tests
go test ./...

# Run tests with coverage
go test -cover ./...
```

## Production Deployment

1. Set environment variables for production
2. Use the provided Dockerfile for containerization
3. Configure Sentry DSN for error tracking
4. Set up proper database backups
5. Configure log aggregation

## Contributing

1. Follow the existing code structure
2. Add tests for new features
3. Update documentation as needed
4. Use conventional commit messages

## License

[Add your license here]

{"INVALID_INPUT": "The provided input is invalid.", "RESOURCE_NOT_FOUND": "The requested resource could not be found.", "INTERNAL_SERVER_ERROR": "An unexpected error occurred. Please try again later.", "SERVICE_UNAVAILABLE": "The service is temporarily unavailable. Please try again later.", "INVALID_CREDENTIALS": "Invalid email or password.", "AUTHORIZATION_REQUIRED": "Authorization header is required.", "INVALID_AUTHORIZATION_FORMAT": "Invalid authorization header format.", "INSUFFICIENT_PERMISSIONS": "You do not have sufficient permissions to access this resource.", "INVALID_REFRESH_TOKEN": "The refresh token is invalid or expired.", "TOKEN_NOT_FOUND": "Authentication token not found.", "ACCOUNT_NOT_FOUND": "The requested account could not be found.", "ACCOUNT_ALREADY_EXISTS": "An account with this email already exists.", "WEAK_PASSWORD": "Password does not meet security requirements.", "INVALID_ACCOUNT_TYPE": "The specified account type is invalid.", "INVALID_REQUEST_DATA": "The request data is invalid or malformed.", "BAD_REQUEST": "The request is invalid.", "UNAUTHORIZED": "You are not authorized to access this resource.", "FORBIDDEN": "Access to this resource is forbidden.", "NOT_FOUND": "The requested resource was not found.", "CONFLICT": "The request conflicts with the current state of the resource."}